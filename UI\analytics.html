<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSID Platform - 数据统计分析</title>

    <style>
        :root { --bg-color: #f6f8fb; --sidebar-bg-color: #ffffff; --card-bg-color: #ffffff; --text-color: #1e293b; --text-color-secondary: #64748b; --border-color: #e5eaf3; --primary-color: #2d78f4; --primary-color-light: #f0f6ff; --shadow-color: rgba(0,0,0,0.06); --icon-color: #64748b; --icon-hover-bg: #f1f5f9; }
        [data-theme="dark"] { --bg-color: #111827; --sidebar-bg-color: #1f2937; --card-bg-color: #1f2937; --text-color: #f9fafb; --text-color-secondary: #9ca3af; --border-color: #374151; --primary-color: #3b82f6; --primary-color-light: #252e3d; --shadow-color: rgba(0,0,0,0.2); --icon-color: #9ca3af; --icon-hover-bg: #374151; }
        * { margin: 0; padding: 0; box-sizing: border-box; font-family: "PingFang SC", "Microsoft YaHei", sans-serif; }
        html, body { height: 100%; overflow: hidden; }
        body { background-color: var(--bg-color); color: var(--text-color); transition: background-color 0.3s, color 0.3s; display: flex; }
        .sidebar { width: 260px; background-color: var(--sidebar-bg-color); border-right: 1px solid var(--border-color); display: flex; flex-direction: column; height: 100%; flex-shrink: 0; transition: background-color 0.3s, border-color 0.3s; }
        .main-wrapper { flex-grow: 1; height: 100%; overflow-y: auto; }
        .main-content { padding: 24px; }
        .sidebar-header { display: flex; align-items: center; padding: 0 24px; height: 64px; border-bottom: 1px solid var(--border-color); flex-shrink: 0; }
        .sidebar-logo { font-size: 22px; font-weight: 700; color: var(--primary-color); }
        .sidebar-nav { flex-grow: 1; padding: 16px 0; }
        .nav-list { list-style: none; }
        .nav-item a { display: flex; align-items: center; gap: 12px; padding: 12px 24px; margin: 4px 16px; border-radius: 8px; color: var(--text-color-secondary); text-decoration: none; font-weight: 500; transition: background-color 0.2s, color 0.2s; }
        .nav-item a:hover { background-color: var(--primary-color-light); color: var(--primary-color); }
        .nav-item.active a { background-color: var(--primary-color-light); color: var(--primary-color); font-weight: 600; }
        .nav-icon { width: 20px; height: 20px; }
        .sub-nav-list { list-style: none; margin-left: 16px; max-height: 0; overflow: hidden; transition: max-height 0.3s ease; }
        .nav-item.expanded .sub-nav-list { max-height: 200px; }
        .sub-nav-list .nav-item a { padding: 8px 24px; margin: 2px 16px; font-size: 13px; }
        .sub-nav-list .nav-item a:before { content: "•"; margin-right: 8px; color: var(--text-color-secondary); }
        .nav-item.has-submenu > a { position: relative; }
        .nav-item.has-submenu > a::after { content: "▶"; position: absolute; right: 16px; font-size: 12px; transition: transform 0.3s ease; }
        .nav-item.has-submenu.expanded > a::after { transform: rotate(90deg); }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .page-title { font-size: 24px; font-weight: 700; }
        .header-actions { display: flex; align-items: center; gap: 16px; }
        .theme-toggle { background-color: var(--icon-hover-bg); border: none; border-radius: 50%; width: 36px; height: 36px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: var(--icon-color); transition: background-color 0.2s; }
        .theme-toggle .icon { width: 20px; height: 20px; }
        #moon-icon { display: none; }
        .card { background-color: var(--card-bg-color); border-radius: 12px; margin-bottom: 24px; box-shadow: 0 4px 12px var(--shadow-color); border: 1px solid var(--border-color); overflow: hidden; transition: background-color 0.3s, border-color 0.3s; }
        .card-header { display: flex; justify-content: space-between; align-items: center; padding: 16px 24px; border-bottom: 1px solid var(--border-color); }
        .card-title { font-size: 17px; font-weight: 600; color: var(--text-color); }
        .card-content { padding: 24px; }
        .filters { display: flex; gap: 16px; align-items: center; flex-wrap: wrap; }
        .filter-group label { font-size: 14px; margin-right: 8px; color: var(--text-color-secondary); }
        .filter-group input, .filter-group select { padding: 8px 12px; border-radius: 6px; border: 1px solid var(--border-color); background-color: var(--card-bg-color); color: var(--text-color); }
        .kpi-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 24px; }
        .kpi-card { position: relative; overflow: hidden; }
        .kpi-card .kpi-title { font-size: 14px; color: var(--text-color-secondary); margin-bottom: 8px; }
        .kpi-card .kpi-value { font-size: 28px; font-weight: 700; color: var(--text-color); margin-bottom: 4px; }
        .kpi-card .kpi-change { font-size: 12px; display: flex; align-items: center; gap: 4px; }
        .kpi-change.positive { color: #28a745; }
        .kpi-change.negative { color: #dc3545; }
        .kpi-icon { position: absolute; top: 16px; right: 16px; }

        .chart-container { position: relative; height: 350px; width: 100%; }
        .chart-grid { display: grid; grid-template-columns: 2fr 1fr; gap: 24px; margin-bottom: 24px; }
        .chart-row { display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px; }

        .btn-primary { background-color: var(--primary-color); color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; transition: all 0.2s; }
        .btn-primary:hover { background-color: #1e5bb8; transform: translateY(-1px); }
        .btn-secondary { background-color: var(--card-bg-color); color: var(--text-color); border: 1px solid var(--border-color); padding: 8px 16px; border-radius: 6px; cursor: pointer; transition: all 0.2s; }
        .btn-secondary:hover { border-color: var(--primary-color); color: var(--primary-color); }

        /* 实时状态指示器 */
        .live-indicator { display: flex; align-items: center; gap: 8px; font-size: 12px; color: var(--text-color-secondary); }
        .pulse-dot { width: 8px; height: 8px; background-color: #28a745; border-radius: 50%; animation: pulse 2s infinite; }
        @keyframes pulse { 0% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); } 70% { transform: scale(1); box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); } 100% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); } }

        /* 算法模块卡片 */
        .algorithm-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 16px; }
        .algorithm-card { background: var(--card-bg-color); border: 1px solid var(--border-color); border-radius: 8px; padding: 16px; transition: all 0.2s; }
        .algorithm-card:hover { border-color: var(--primary-color); box-shadow: 0 4px 12px rgba(45, 120, 244, 0.1); }
        .algorithm-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; }
        .algorithm-name { font-weight: 600; color: var(--text-color); }
        .algorithm-status { padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 500; }
        .status-normal { background-color: rgba(40, 167, 69, 0.1); color: #28a745; }
        .status-warning { background-color: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .algorithm-metrics { display: grid; grid-template-columns: 1fr 1fr; gap: 12px; }
        .metric-item { text-align: center; }
        .metric-value { font-size: 18px; font-weight: 600; color: var(--text-color); }
        .metric-label { font-size: 11px; color: var(--text-color-secondary); margin-top: 2px; }
    </style>
</head>
<body data-theme="light">

    <aside class="sidebar">
        <div class="sidebar-header"><h1 class="sidebar-logo">SSID Platform</h1></div>
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item"><a href="dashboard.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path></svg><span>首页</span></a></li>
                <li class="nav-item"><a href="configuration.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg><span>配置管理</span></a></li>
                <li class="nav-item active"><a href="analytics.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg><span>数据统计</span></a></li>
                <li class="nav-item"><a href="risk-management.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg><span>风险管控</span></a></li>
                <li class="nav-item"><a href="case-audit.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg><span>案例审计</span></a></li>
                <li class="nav-item has-submenu" onclick="toggleSubmenu(this)">
                    <a href="javascript:void(0)">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path></svg>
                        <span>特征库管理</span>
                    </a>
                    <ul class="sub-nav-list">
                        <li class="nav-item"><a href="face-feature-library.html">人脸特征库</a></li>
                        <li class="nav-item"><a href="background-fingerprint-library.html">背景特征库</a></li>
                    </ul>
                </li>
                <li class="nav-item"><a href="distribution.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path></svg><span>资源分发</span></a></li>
                <li class="nav-item"><a href="settings.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 16v-2m8-8h2M4 12H2m15.364 6.364l-1.414-1.414M6.05 6.05L4.636 4.636m12.728 12.728L15.95 15.95M6.05 17.95l1.414-1.414M12 18a6 6 0 100-12 6 6 0 000 12z"></path></svg><span>系统管理</span></a></li>
            </ul>
        </nav>
    </aside>

    <div class="main-wrapper">
        <main class="main-content">
            <header class="page-header">
                <h1 class="page-title">数据统计分析</h1>
                <div class="header-actions">
                    <button class="theme-toggle" id="theme-toggle-btn" title="切换亮/暗模式"><svg id="sun-icon" class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path></svg><svg id="moon-icon" class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path></svg></button>
                </div>
            </header>

            <!-- 筛选条件 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">数据筛选</h2>
                    <div class="live-indicator">
                        <div class="pulse-dot"></div>
                        <span>实时更新</span>
                    </div>
                </div>
                <div class="card-content filters">
                    <div class="filter-group">
                        <label for="date-range">时间范围:</label>
                        <select id="time-range">
                            <option value="1h">近1小时</option>
                            <option value="24h" selected>近24小时</option>
                            <option value="7d">近7天</option>
                            <option value="30d">近30天</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                    <div class="filter-group" id="custom-date" style="display: none;">
                        <input type="date" id="date-start">
                        <span style="margin: 0 8px;">至</span>
                        <input type="date" id="date-end">
                    </div>
                    <div class="filter-group">
                        <label for="channel">渠道:</label>
                        <select id="channel">
                            <option value="">全部渠道</option>
                            <option value="mobile_bank">手机银行</option>
                            <option value="web_bank">网上银行</option>
                            <option value="mini_program">小程序</option>
                            <option value="app">移动应用</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="scenario">业务场景:</label>
                        <select id="scenario">
                            <option value="">全部场景</option>
                            <option value="login">用户登录</option>
                            <option value="register">用户注册</option>
                            <option value="transfer">大额转账</option>
                            <option value="payment">支付确认</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="algorithm">算法类型:</label>
                        <select id="algorithm">
                            <option value="">全部算法</option>
                            <option value="liveness">活体检测</option>
                            <option value="deepfake">深伪检测</option>
                            <option value="background">相似背景</option>
                            <option value="quality">图像质量</option>
                        </select>
                    </div>
                    <button class="btn-primary" onclick="applyFilters()">查询</button>
                    <button class="btn-secondary" onclick="resetFilters()">重置</button>
                    <button class="btn-secondary" onclick="exportData()">导出</button>
                </div>
            </div>

            <!-- 核心指标 -->
            <div class="kpi-grid">
                <div class="card kpi-card">
                    <div class="card-content">
                        <div class="kpi-icon" style="font-size: 16px; font-weight: bold; color: #2d78f4; background: rgba(45,120,244,0.1); padding: 8px; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">总</div>
                        <div class="kpi-title">今日核验总数</div>
                        <div class="kpi-value">12,847</div>
                        <div class="kpi-change positive">
                            ▲ +8.5%
                        </div>
                    </div>
                </div>
                <div class="card kpi-card">
                    <div class="card-content">
                        <div class="kpi-icon" style="font-size: 16px; font-weight: bold; color: #28a745; background: rgba(40,167,69,0.1); padding: 8px; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">通</div>
                        <div class="kpi-title">核验通过率</div>
                        <div class="kpi-value">86.2%</div>
                        <div class="kpi-change negative">
                            ▼ -1.2%
                        </div>
                    </div>
                </div>
                <div class="card kpi-card">
                    <div class="card-content">
                        <div class="kpi-icon" style="font-size: 16px; font-weight: bold; color: #ffc107; background: rgba(255,193,7,0.1); padding: 8px; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">算</div>
                        <div class="kpi-title">算法拦截数</div>
                        <div class="kpi-value">1,768</div>
                        <div class="kpi-change positive">
                            ▲ +12.3%
                        </div>
                    </div>
                </div>
                <div class="card kpi-card">
                    <div class="card-content">
                        <div class="kpi-icon" style="font-size: 16px; font-weight: bold; color: #6f42c1; background: rgba(111,66,193,0.1); padding: 8px; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">风</div>
                        <div class="kpi-title">风控拦截数</div>
                        <div class="kpi-value">342</div>
                        <div class="kpi-change negative">
                            ▼ -5.8%
                        </div>
                    </div>
                </div>
                <div class="card kpi-card">
                    <div class="card-content">
                        <div class="kpi-icon" style="font-size: 16px; font-weight: bold; color: #17a2b8; background: rgba(23,162,184,0.1); padding: 8px; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">时</div>
                        <div class="kpi-title">平均响应时间</div>
                        <div class="kpi-value">85ms</div>
                        <div class="kpi-change positive">
                            ▼ -15ms
                        </div>
                    </div>
                </div>
                <div class="card kpi-card">
                    <div class="card-content">
                        <div class="kpi-icon" style="font-size: 16px; font-weight: bold; color: #28a745; background: rgba(40,167,69,0.1); padding: 8px; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">用</div>
                        <div class="kpi-title">系统可用性</div>
                        <div class="kpi-value">99.9%</div>
                        <div class="kpi-change positive">
                            ▲ +0.1%
                        </div>
                    </div>
                </div>
            </div>

            <!-- 算法模块监控 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">算法模块监控</h2>
                    <button class="btn-secondary" onclick="refreshAlgorithmStatus()">刷新状态</button>
                </div>
                <div class="card-content">
                    <div class="algorithm-grid">
                        <div class="algorithm-card">
                            <div class="algorithm-header">
                                <div class="algorithm-name">活体检测</div>
                                <div class="algorithm-status status-normal">正常</div>
                            </div>
                            <div class="algorithm-metrics">
                                <div class="metric-item">
                                    <div class="metric-value">856</div>
                                    <div class="metric-label">今日拦截</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">94.2%</div>
                                    <div class="metric-label">准确率</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">45ms</div>
                                    <div class="metric-label">响应时间</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">6.7%</div>
                                    <div class="metric-label">拦截率</div>
                                </div>
                            </div>
                        </div>

                        <div class="algorithm-card">
                            <div class="algorithm-header">
                                <div class="algorithm-name">深伪检测</div>
                                <div class="algorithm-status status-normal">正常</div>
                            </div>
                            <div class="algorithm-metrics">
                                <div class="metric-item">
                                    <div class="metric-value">342</div>
                                    <div class="metric-label">今日拦截</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">96.8%</div>
                                    <div class="metric-label">准确率</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">128ms</div>
                                    <div class="metric-label">响应时间</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">2.7%</div>
                                    <div class="metric-label">拦截率</div>
                                </div>
                            </div>
                        </div>

                        <div class="algorithm-card">
                            <div class="algorithm-header">
                                <div class="algorithm-name">相似背景检测</div>
                                <div class="algorithm-status status-warning">告警</div>
                            </div>
                            <div class="algorithm-metrics">
                                <div class="metric-item">
                                    <div class="metric-value">228</div>
                                    <div class="metric-label">今日拦截</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">89.3%</div>
                                    <div class="metric-label">准确率</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">95ms</div>
                                    <div class="metric-label">响应时间</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">1.8%</div>
                                    <div class="metric-label">拦截率</div>
                                </div>
                            </div>
                        </div>

                        <div class="algorithm-card">
                            <div class="algorithm-header">
                                <div class="algorithm-name">图像质量检测</div>
                                <div class="algorithm-status status-normal">正常</div>
                            </div>
                            <div class="algorithm-metrics">
                                <div class="metric-item">
                                    <div class="metric-value">156</div>
                                    <div class="metric-label">今日拦截</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">97.5%</div>
                                    <div class="metric-label">准确率</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">32ms</div>
                                    <div class="metric-label">响应时间</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">1.2%</div>
                                    <div class="metric-label">拦截率</div>
                                </div>
                            </div>
                        </div>

                        <div class="algorithm-card">
                            <div class="algorithm-header">
                                <div class="algorithm-name">相似度检测</div>
                                <div class="algorithm-status status-normal">正常</div>
                            </div>
                            <div class="algorithm-metrics">
                                <div class="metric-item">
                                    <div class="metric-value">89</div>
                                    <div class="metric-label">今日拦截</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">98.1%</div>
                                    <div class="metric-label">准确率</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">78ms</div>
                                    <div class="metric-label">响应时间</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">0.7%</div>
                                    <div class="metric-label">拦截率</div>
                                </div>
                            </div>
                        </div>

                        <div class="algorithm-card">
                            <div class="algorithm-header">
                                <div class="algorithm-name">人脸检测</div>
                                <div class="algorithm-status status-normal">正常</div>
                            </div>
                            <div class="algorithm-metrics">
                                <div class="metric-item">
                                    <div class="metric-value">97</div>
                                    <div class="metric-label">今日拦截</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">99.6%</div>
                                    <div class="metric-label">准确率</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">25ms</div>
                                    <div class="metric-label">响应时间</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">0.8%</div>
                                    <div class="metric-label">拦截率</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表展示 -->
            <div class="chart-grid">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">核验趋势分析</h2>
                        <select class="btn-secondary" style="padding: 4px 8px;">
                            <option>按小时</option>
                            <option>按天</option>
                            <option>按周</option>
                        </select>
                    </div>
                    <div class="card-content">
                        <div style="padding: 20px;">
                            <h4 style="margin-bottom: 16px; color: var(--text-color);">核验趋势 (最近6小时)</h4>
                            <div style="display: flex; align-items: end; gap: 12px; height: 250px; background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #e9ecef;">
                                <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                                    <div style="background: linear-gradient(to top, #2d78f4, #4a90e2); width: 40px; height: 120px; margin-bottom: 8px; border-radius: 4px 4px 0 0; box-shadow: 0 2px 4px rgba(45,120,244,0.3);"></div>
                                    <span style="font-size: 12px; color: var(--text-color-secondary); font-weight: 500;">00:00</span>
                                    <span style="font-size: 10px; color: var(--text-color-secondary); margin-top: 2px;">450</span>
                                </div>
                                <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                                    <div style="background: linear-gradient(to top, #28a745, #34ce57); width: 40px; height: 160px; margin-bottom: 8px; border-radius: 4px 4px 0 0; box-shadow: 0 2px 4px rgba(40,167,69,0.3);"></div>
                                    <span style="font-size: 12px; color: var(--text-color-secondary); font-weight: 500;">04:00</span>
                                    <span style="font-size: 10px; color: var(--text-color-secondary); margin-top: 2px;">620</span>
                                </div>
                                <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                                    <div style="background: linear-gradient(to top, #ffc107, #ffcd39); width: 40px; height: 140px; margin-bottom: 8px; border-radius: 4px 4px 0 0; box-shadow: 0 2px 4px rgba(255,193,7,0.3);"></div>
                                    <span style="font-size: 12px; color: var(--text-color-secondary); font-weight: 500;">08:00</span>
                                    <span style="font-size: 10px; color: var(--text-color-secondary); margin-top: 2px;">580</span>
                                </div>
                                <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                                    <div style="background: linear-gradient(to top, #dc3545, #e74c3c); width: 40px; height: 180px; margin-bottom: 8px; border-radius: 4px 4px 0 0; box-shadow: 0 2px 4px rgba(220,53,69,0.3);"></div>
                                    <span style="font-size: 12px; color: var(--text-color-secondary); font-weight: 500;">12:00</span>
                                    <span style="font-size: 10px; color: var(--text-color-secondary); margin-top: 2px;">720</span>
                                </div>
                                <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                                    <div style="background: linear-gradient(to top, #17a2b8, #20c997); width: 40px; height: 150px; margin-bottom: 8px; border-radius: 4px 4px 0 0; box-shadow: 0 2px 4px rgba(23,162,184,0.3);"></div>
                                    <span style="font-size: 12px; color: var(--text-color-secondary); font-weight: 500;">16:00</span>
                                    <span style="font-size: 10px; color: var(--text-color-secondary); margin-top: 2px;">600</span>
                                </div>
                                <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                                    <div style="background: linear-gradient(to top, #6f42c1, #8e44ad); width: 40px; height: 130px; margin-bottom: 8px; border-radius: 4px 4px 0 0; box-shadow: 0 2px 4px rgba(111,66,193,0.3);"></div>
                                    <span style="font-size: 12px; color: var(--text-color-secondary); font-weight: 500;">20:00</span>
                                    <span style="font-size: 10px; color: var(--text-color-secondary); margin-top: 2px;">520</span>
                                </div>
                            </div>
                            <div style="margin-top: 16px; display: flex; gap: 16px; font-size: 12px; justify-content: center;">
                                <div style="display: flex; align-items: center; gap: 4px;">
                                    <div style="width: 12px; height: 12px; background: #2d78f4; border-radius: 2px;"></div>
                                    <span>总核验数</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 4px;">
                                    <div style="width: 12px; height: 12px; background: #28a745; border-radius: 2px;"></div>
                                    <span>通过数</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 4px;">
                                    <div style="width: 12px; height: 12px; background: #dc3545; border-radius: 2px;"></div>
                                    <span>拦截数</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">拦截类型分布</h2>
                    </div>
                    <div class="card-content">
                        <div style="padding: 20px;">
                            <h4 style="margin-bottom: 16px; color: var(--text-color);">拦截类型分布</h4>
                            <div style="display: flex; align-items: center; gap: 24px;">
                                <div style="width: 120px; height: 120px; border-radius: 50%; background: conic-gradient(#ffc107 0deg 154deg, #fd7e14 154deg 232deg, #e83e8c 232deg 287deg, #6f42c1 287deg 323deg, #20c997 323deg 344deg, #17a2b8 344deg 360deg);"></div>
                                <div style="display: flex; flex-direction: column; gap: 8px; font-size: 14px;">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <div style="width: 12px; height: 12px; background: #ffc107; border-radius: 2px;"></div>
                                        <span>活体检测 (856)</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <div style="width: 12px; height: 12px; background: #fd7e14; border-radius: 2px;"></div>
                                        <span>深伪检测 (342)</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <div style="width: 12px; height: 12px; background: #e83e8c; border-radius: 2px;"></div>
                                        <span>相似背景 (228)</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <div style="width: 12px; height: 12px; background: #6f42c1; border-radius: 2px;"></div>
                                        <span>图像质量 (156)</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <div style="width: 12px; height: 12px; background: #20c997; border-radius: 2px;"></div>
                                        <span>相似度 (89)</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <div style="width: 12px; height: 12px; background: #17a2b8; border-radius: 2px;"></div>
                                        <span>人脸检测 (97)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="chart-row">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">渠道分布统计</h2>
                    </div>
                    <div class="card-content">
                        <div style="padding: 20px;">
                            <h4 style="margin-bottom: 16px; color: var(--text-color);">渠道分布统计</h4>
                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="width: 80px; font-size: 14px;">手机银行</span>
                                    <div style="flex: 1; height: 24px; background: #e9ecef; border-radius: 12px; overflow: hidden;">
                                        <div style="width: 65%; height: 100%; background: #2d78f4; border-radius: 12px;"></div>
                                    </div>
                                    <span style="font-size: 14px; color: var(--text-color-secondary);">5,420</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="width: 80px; font-size: 14px;">网上银行</span>
                                    <div style="flex: 1; height: 24px; background: #e9ecef; border-radius: 12px; overflow: hidden;">
                                        <div style="width: 40%; height: 100%; background: #28a745; border-radius: 12px;"></div>
                                    </div>
                                    <span style="font-size: 14px; color: var(--text-color-secondary);">3,280</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="width: 80px; font-size: 14px;">小程序</span>
                                    <div style="flex: 1; height: 24px; background: #e9ecef; border-radius: 12px; overflow: hidden;">
                                        <div style="width: 35%; height: 100%; background: #ffc107; border-radius: 12px;"></div>
                                    </div>
                                    <span style="font-size: 14px; color: var(--text-color-secondary);">2,847</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="width: 80px; font-size: 14px;">移动应用</span>
                                    <div style="flex: 1; height: 24px; background: #e9ecef; border-radius: 12px; overflow: hidden;">
                                        <div style="width: 16%; height: 100%; background: #dc3545; border-radius: 12px;"></div>
                                    </div>
                                    <span style="font-size: 14px; color: var(--text-color-secondary);">1,300</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">业务场景分析</h2>
                    </div>
                    <div class="card-content">
                        <div style="padding: 20px;">
                            <h4 style="margin-bottom: 16px; color: var(--text-color);">业务场景分析</h4>
                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="width: 80px; font-size: 14px;">用户登录</span>
                                    <div style="flex: 1; height: 24px; background: #e9ecef; border-radius: 12px; overflow: hidden;">
                                        <div style="width: 70%; height: 100%; background: #2d78f4; border-radius: 12px;"></div>
                                    </div>
                                    <span style="font-size: 14px; color: var(--text-color-secondary);">4,200</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="width: 80px; font-size: 14px;">用户注册</span>
                                    <div style="flex: 1; height: 24px; background: #e9ecef; border-radius: 12px; overflow: hidden;">
                                        <div style="width: 52%; height: 100%; background: #28a745; border-radius: 12px;"></div>
                                    </div>
                                    <span style="font-size: 14px; color: var(--text-color-secondary);">3,100</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="width: 80px; font-size: 14px;">大额转账</span>
                                    <div style="flex: 1; height: 24px; background: #e9ecef; border-radius: 12px; overflow: hidden;">
                                        <div style="width: 47%; height: 100%; background: #ffc107; border-radius: 12px;"></div>
                                    </div>
                                    <span style="font-size: 14px; color: var(--text-color-secondary);">2,800</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="width: 80px; font-size: 14px;">支付确认</span>
                                    <div style="flex: 1; height: 24px; background: #e9ecef; border-radius: 12px; overflow: hidden;">
                                        <div style="width: 32%; height: 100%; background: #17a2b8; border-radius: 12px;"></div>
                                    </div>
                                    <span style="font-size: 14px; color: var(--text-color-secondary);">1,900</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="width: 80px; font-size: 14px;">信息修改</span>
                                    <div style="flex: 1; height: 24px; background: #e9ecef; border-radius: 12px; overflow: hidden;">
                                        <div style="width: 14%; height: 100%; background: #6f42c1; border-radius: 12px;"></div>
                                    </div>
                                    <span style="font-size: 14px; color: var(--text-color-secondary);">847</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </main>
    </div>

    <script>
        // --- THEME SCRIPT ---
        const themeToggleBtn = document.getElementById('theme-toggle-btn');
        const sunIcon = document.getElementById('sun-icon');
        const moonIcon = document.getElementById('moon-icon');
        const body = document.body;

        const applyTheme = (theme) => {
            body.setAttribute('data-theme', theme);
            if (theme === 'dark') {
                sunIcon.style.display = 'none';
                moonIcon.style.display = 'block';
            } else {
                sunIcon.style.display = 'block';
                moonIcon.style.display = 'none';
            }
        };

        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        const defaultTheme = savedTheme || (prefersDark ? 'dark' : 'light');
        applyTheme(defaultTheme);

        themeToggleBtn.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            localStorage.setItem('theme', newTheme);
            applyTheme(newTheme);
        });



        // 新增功能函数
        function applyFilters() {
            const timeRange = document.getElementById('time-range').value;
            const channel = document.getElementById('channel').value;
            const scenario = document.getElementById('scenario').value;
            const algorithm = document.getElementById('algorithm').value;

            console.log('应用筛选条件:', { timeRange, channel, scenario, algorithm });
            alert('筛选条件已应用！正在更新数据...');
        }

        function resetFilters() {
            document.getElementById('time-range').value = '24h';
            document.getElementById('channel').value = '';
            document.getElementById('scenario').value = '';
            document.getElementById('algorithm').value = '';
            const customDate = document.getElementById('custom-date');
            if (customDate) customDate.style.display = 'none';
        }

        function exportData() {
            alert('数据导出功能\n\n将导出当前筛选条件下的统计数据，包括：\n- 核心指标数据\n- 算法模块性能\n- 趋势分析数据\n- 分布统计数据');
        }

        function refreshAlgorithmStatus() {
            alert('刷新算法状态\n\n正在获取最新的算法模块状态...');
            setTimeout(() => {
                alert('算法状态已更新！');
            }, 1500);
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            const timeRangeSelect = document.getElementById('time-range');
            const customDate = document.getElementById('custom-date');

            if (timeRangeSelect && customDate) {
                timeRangeSelect.addEventListener('change', function() {
                    if (this.value === 'custom') {
                        customDate.style.display = 'flex';
                    } else {
                        customDate.style.display = 'none';
                    }
                });
            }

            // 子菜单展开/收起功能
            window.toggleSubmenu = function(element) {
                element.classList.toggle('expanded');
            };
        });
    </script>
</body>
</html>