<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSID Platform - 背景指纹库</title>
    <!-- [此处省略了与之前版本相同的CSS样式代码] -->
    <style>
        :root { --bg-color: #f6f8fb; --sidebar-bg-color: #ffffff; --card-bg-color: #ffffff; --text-color: #1e293b; --text-color-secondary: #64748b; --border-color: #e5eaf3; --primary-color: #2d78f4; --primary-color-light: #f0f6ff; --shadow-color: rgba(0,0,0,0.06); --icon-color: #9ca3af; --icon-hover-bg: #f1f5f9; }
        [data-theme="dark"] { --bg-color: #111827; --sidebar-bg-color: #1f2937; --card-bg-color: #1f2937; --text-color: #f9fafb; --text-color-secondary: #9ca3af; --border-color: #374151; --primary-color: #3b82f6; --primary-color-light: #252e3d; --shadow-color: rgba(0,0,0,0.2); --icon-color: #9ca3af; --icon-hover-bg: #374151; }
        * { margin: 0; padding: 0; box-sizing: border-box; font-family: "PingFang SC", "Microsoft YaHei", sans-serif; }
        html, body { height: 100%; overflow: hidden; }
        body { background-color: var(--bg-color); color: var(--text-color); transition: background-color 0.3s, color 0.3s; display: flex; }
        .sidebar { width: 260px; background-color: var(--sidebar-bg-color); border-right: 1px solid var(--border-color); display: flex; flex-direction: column; height: 100%; flex-shrink: 0; transition: background-color 0.3s, border-color 0.3s; }
        .sidebar-header { display: flex; align-items: center; padding: 0 24px; height: 64px; border-bottom: 1px solid var(--border-color); flex-shrink: 0; }
        .sidebar-logo { font-size: 22px; font-weight: 700; color: var(--primary-color); }
        .sidebar-nav { flex-grow: 1; padding: 16px 0; }
        .nav-list { list-style: none; }
        .nav-item a { display: flex; align-items: center; gap: 12px; padding: 12px 24px; margin: 4px 16px; border-radius: 8px; color: var(--text-color-secondary); text-decoration: none; font-weight: 500; transition: background-color 0.2s, color 0.2s; }
        .nav-item a:hover { background-color: var(--primary-color-light); color: var(--primary-color); }
        .nav-item.active a { background-color: var(--primary-color-light); color: var(--primary-color); font-weight: 600; }
        .nav-icon { width: 20px; height: 20px; }
        .sub-nav-list { list-style: none; margin-left: 16px; }
        .sub-nav-list .nav-item a { padding: 8px 24px; margin: 2px 16px; font-size: 13px; }
        .sub-nav-list .nav-item a:before { content: "•"; margin-right: 8px; color: var(--text-color-secondary); }
        .main-wrapper { flex-grow: 1; height: 100%; overflow-y: auto; }
        .main-content { padding: 24px; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .page-title { font-size: 24px; font-weight: 700; }
        .btn { padding: 8px 16px; border-radius: 6px; border: none; font-size: 14px; cursor: pointer; font-weight: 500; }
        .btn-primary { background-color: var(--primary-color); color: white; }
        .btn-secondary { background-color: var(--card-bg-color); color: var(--text-color); border: 1px solid var(--border-color); }
        .btn-danger { background-color: #ff3b30; color: white; }
        .card { background-color: var(--card-bg-color); border-radius: 12px; margin-bottom: 24px; border: 1px solid var(--border-color); }
        .card-header { padding: 16px 24px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center; }
        .card-title { font-size: 17px; font-weight: 600; }
        .card-content { padding: 0; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { text-align: left; padding: 12px 16px; border-bottom: 1px solid var(--border-color); vertical-align: middle; }
        .table th { color: var(--text-color-secondary); font-weight: 500; }
        .action-buttons { display: flex; gap: 8px; }
        .risk-badge { padding: 3px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; display: inline-block; }
        .risk-high { background-color: rgba(255, 59, 48, 0.15); color: #ff3b30; }
        .risk-medium { background-color: rgba(255, 149, 0, 0.15); color: #ff9500; }
        .bg-thumbnail { width: 120px; height: 67.5px; object-fit: cover; border-radius: 4px; }
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); align-items: center; justify-content: center; }
        .modal.active { display: flex; }
        .modal-content { background-color: var(--card-bg-color); padding: 24px; width: 90%; max-width: 500px; border-radius: 12px; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .modal-title { font-size: 18px; font-weight: 600; }
        .modal-close { cursor: pointer; font-size: 24px; }
        .modal-footer { margin-top: 24px; display: flex; justify-content: flex-end; gap: 12px; }
        .form-group { margin-bottom: 16px; }
        .form-label { display: block; font-weight: 500; margin-bottom: 8px; }
        .form-input, .form-select { width: 100%; padding: 10px 12px; border: 1px solid var(--border-color); border-radius: 6px; }
        .notice { padding: 12px; background-color: var(--primary-color-light); border-radius: 6px; font-size: 13px; color: var(--primary-color); margin-bottom: 16px; }
    </style>
</head>
<body>
    <aside class="sidebar">
        <div class="sidebar-header"><h1 class="sidebar-logo">SSID Platform</h1></div>
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="dashboard.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path></svg>
                        <span>首页</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="configuration.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>
                        <span>配置管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="analytics.html">
                       <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>
                        <span>数据统计</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="risk-management.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg>
                        <span>风险管控</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="case-audit.html">
                       <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>
                        <span>案例审计</span>
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="feature-sets.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path></svg>
                        <span>特征库管理</span>
                    </a>
                    <ul class="sub-nav-list">
                        <li class="nav-item"><a href="face-feature-library.html">人脸特征库</a></li>
                        <li class="nav-item active"><a href="background-fingerprint-library.html">背景特征库</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a href="distribution.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path></svg>
                        <span>资源分发</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="settings.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 16v-2m8-8h2M4 12H2m15.364 6.364l-1.414-1.414M6.05 6.05L4.636 4.636m12.728 12.728L15.95 15.95M6.05 17.95l1.414-1.414M12 18a6 6 0 100-12 6 6 0 000 12z"></path></svg>
                        <span>系统管理</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <div class="main-wrapper">
        <main class="main-content">
            <!-- 库列表 (主视图) -->
            <div id="library-list-view">
                 <header class="page-header">
                    <h1 class="page-title">背景指纹库</h1>
                    <div>
                        <button class="btn btn-secondary" onclick="openBatchAnalysisModal()">批量相似性分析</button>
                        <button class="btn btn-primary" onclick="openNewLibraryModal()">+ 新建指纹库</button>
                    </div>
                </header>
                 <div class="card">
                    <div class="card-content">
                        <table class="table">
                            <thead><tr><th>指纹库名称</th><th>指纹数量</th><th>匹配统计</th><th>备注</th><th>创建时间</th><th>操作</th></tr></thead>
                            <tbody>
                                <tr><td>已知欺诈背景库</td><td>138</td><td><span style="color: #ff3b30; font-weight: 600;">拦截 23 次</span></td><td>来自历史欺诈案例</td><td>2023-08-01</td><td class="action-buttons"><button class="btn btn-secondary" onclick="showFingerprintManagementView()">管理指纹</button><button class="btn btn-secondary">编辑</button><button class="btn btn-secondary">查看拦截记录</button></td></tr>
                                <tr><td>疑似模板背景库</td><td>42</td><td><span style="color: #ff9500; font-weight: 600;">预警 8 次</span></td><td>来自安全审计中心筛查结果</td><td>2023-09-10</td><td class="action-buttons"><button class="btn btn-secondary" onclick="showFingerprintManagementView()">管理指纹</button><button class="btn btn-secondary">编辑</button><button class="btn btn-secondary">查看预警记录</button></td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 指纹管理 (详情视图, 默认隐藏) -->
             <div id="fingerprint-management-view" style="display: none;">
                 <header class="page-header">
                    <div>
                        <button class="btn btn-secondary" onclick="showLibraryListView()">&larr; 返回库列表</button>
                        <h1 class="page-title" style="display: inline-block; margin-left: 16px;">管理指纹: 已知欺诈背景库</h1>
                    </div>
                    <div>
                        <input type="search" class="form-input" placeholder="按备注或ID搜索..." style="display:inline-block; width: 250px; vertical-align: middle; margin-right: 16px;">
                        <button class="btn btn-secondary" onclick="openBatchImportModal()">从案例审计导入</button>
                        <button class="btn btn-primary" onclick="openAddFingerprintModal()">+ 手动添加指纹</button>
                    </div>
                </header>
                 <div class="card">
                     <div class="card-content">
                        <table class="table">
                            <thead><tr><th>背景缩略图</th><th>风险等级</th><th>备注/来源</th><th>入库时间</th><th>操作</th></tr></thead>
                            <tbody>
                                <tr><td><img src="https://picsum.photos/seed/bg1/200/112" class="bg-thumbnail"></td><td><span class="risk-badge risk-high">高风险</span></td><td>2025-09-15 XX活动欺诈案例</td><td>2025-09-15 14:30</td><td class="action-buttons"><button class="btn btn-secondary">编辑</button><button class="btn-danger" style="background: none; color: var(--danger-color);" onclick="openDeleteModal()">删除</button></td></tr>
                                <tr><td><img src="https://picsum.photos/seed/bg2/200/112" class="bg-thumbnail"></td><td><span class="risk-badge risk-medium">中风险</span></td><td>来自安全审计-风险簇A</td><td>2025-09-14 10:18</td><td class="action-buttons"><button class="btn btn-secondary">编辑</button><button class="btn-danger" style="background: none; color: var(--danger-color);" onclick="openDeleteModal()">删除</button></td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="new-library-modal" class="modal"><div class="modal-content"><div class="modal-header"><h3 class="modal-title">新建背景指纹库</h3><span class="modal-close" onclick="closeAllModals()">&times;</span></div><div class="form-group"><label class="form-label">指纹库名称</label><input type="text" class="form-input"></div><div class="form-group"><label class="form-label">备注</label><input type="text" class="form-input"></div><div class="modal-footer"><button class="btn btn-secondary" onclick="closeAllModals()">取消</button><button class="btn btn-primary">创建</button></div></div></div>
    <div id="add-fingerprint-modal" class="modal"><div class="modal-content"><div class="modal-header"><h3 class="modal-title">手动添加背景指纹</h3><span class="modal-close" onclick="closeAllModals()">&times;</span></div><div class="notice"><strong>提示:</strong> 推荐从“案例审计”或“安全审计中心”将风险案例一键添加至此，以确保指纹的准确性。</div><div class="form-group"><label class="form-label">上传背景图片</label><input type="file" class="form-input" accept="image/*"></div><div class="form-group"><label class="form-label">风险等级</label><select class="form-select"><option>高风险</option><option>中风险</option><option>低风险</option></select></div><div class="form-group"><label class="form-label">备注/来源</label><input type="text" class="form-input"></div><div class="modal-footer"><button class="btn btn-secondary" onclick="closeAllModals()">取消</button><button class="btn btn-primary">确认添加</button></div></div></div>
    <div id="delete-modal" class="modal"><div class="modal-content"><div class="modal-header"><h3 class="modal-title">确认删除</h3><span class="modal-close" onclick="closeAllModals()">&times;</span></div><p>您确定要删除这个背景指纹吗？此操作无法撤销。</p><div class="modal-footer"><button class="btn btn-secondary" onclick="closeAllModals()">取消</button><button class="btn btn-danger">确认删除</button></div></div></div>

    <!-- 批量相似性分析模态框 -->
    <div id="batch-analysis-modal" class="modal">
        <div class="modal-content" style="max-width: 700px;">
            <div class="modal-header">
                <h3 class="modal-title">批量相似性分析</h3>
                <span class="modal-close" onclick="closeAllModals()">&times;</span>
            </div>
            <div class="notice">
                <strong>功能说明:</strong> 对存量数据进行背景相似性检测，发现可能使用相同视频模板但人脸不同的风险案例。
            </div>
            <div class="form-group">
                <label class="form-label">分析时间范围</label>
                <div style="display: flex; gap: 12px; align-items: center;">
                    <input type="date" class="form-input" style="width: 200px;">
                    <span>至</span>
                    <input type="date" class="form-input" style="width: 200px;">
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">相似度阈值</label>
                <select class="form-select">
                    <option>高相似度 (≥0.9)</option>
                    <option selected>中等相似度 (≥0.7)</option>
                    <option>低相似度 (≥0.5)</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">分析范围 - 按渠道场景</label>
                <select class="form-select">
                    <option>全部渠道场景</option>
                    <option>APP - 用户注册</option>
                    <option>APP - 用户登录</option>
                    <option>APP - 实名认证</option>
                    <option>小程序 - 用户注册</option>
                    <option>小程序 - 身份验证</option>
                    <option>H5 - 开户验证</option>
                    <option>H5 - 交易验证</option>
                    <option>PC端 - 登录验证</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">数据类型</label>
                <select class="form-select">
                    <option>全部数据</option>
                    <option>仅风险案例</option>
                    <option>仅正常案例</option>
                </select>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeAllModals()">取消</button>
                <button class="btn btn-primary">开始分析</button>
            </div>
        </div>
    </div>

    <!-- 从案例审计导入模态框 -->
    <div id="batch-import-modal" class="modal">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3 class="modal-title">从案例审计导入</h3>
                <span class="modal-close" onclick="closeAllModals()">&times;</span>
            </div>
            <div class="notice">
                <strong>推荐操作:</strong> 从案例审计中选择已确认的风险案例，一键导入背景指纹库。
            </div>
            <div class="form-group">
                <label class="form-label">选择案例来源</label>
                <select class="form-select">
                    <option>近30天风险案例</option>
                    <option>近90天风险案例</option>
                    <option>历史欺诈案例</option>
                    <option>人工标记案例</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">风险等级设置</label>
                <select class="form-select">
                    <option>高风险</option>
                    <option selected>中风险</option>
                    <option>低风险</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">目标指纹库</label>
                <select class="form-select">
                    <option>已知欺诈背景库</option>
                    <option selected>疑似模板背景库</option>
                </select>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeAllModals()">取消</button>
                <button class="btn btn-primary">确认导入</button>
            </div>
        </div>
    </div>

    <script>
        function showView(viewIdToShow) {
            document.getElementById('library-list-view').style.display = 'none';
            document.getElementById('fingerprint-management-view').style.display = 'none';
            document.getElementById(viewIdToShow).style.display = 'block';
        }
        function showLibraryListView() { showView('library-list-view'); }
        function showFingerprintManagementView() { showView('fingerprint-management-view'); }
        function openNewLibraryModal() { document.getElementById('new-library-modal').classList.add('active'); }
        function openAddFingerprintModal() { document.getElementById('add-fingerprint-modal').classList.add('active'); }
        function openDeleteModal() { document.getElementById('delete-modal').classList.add('active'); }
        function openBatchAnalysisModal() { document.getElementById('batch-analysis-modal').classList.add('active'); }
        function openBatchImportModal() { document.getElementById('batch-import-modal').classList.add('active'); }
        function closeAllModals() { document.querySelectorAll('.modal').forEach(m => m.classList.remove('active')); }
    </script>
</body>
</html>