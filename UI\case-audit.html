<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSID Platform - 案例审查中心</title>
    <style>
        :root { --bg-color: #f6f8fb; --sidebar-bg-color: #ffffff; --card-bg-color: #ffffff; --text-color: #1e293b; --text-color-secondary: #64748b; --border-color: #e5eaf3; --primary-color: #2d78f4; --primary-color-light: #f0f6ff; --shadow-color: rgba(0,0,0,0.06); --icon-color: #64748b; --icon-hover-bg: #f1f5f9; }
        [data-theme="dark"] { --bg-color: #111827; --sidebar-bg-color: #1f2937; --card-bg-color: #1f2937; --text-color: #f9fafb; --text-color-secondary: #9ca3af; --border-color: #374151; --primary-color: #3b82f6; --primary-color-light: #252e3d; --shadow-color: rgba(0,0,0,0.2); --icon-color: #9ca3af; --icon-hover-bg: #374151; }
        * { margin: 0; padding: 0; box-sizing: border-box; font-family: "PingFang SC", "Microsoft YaHei", sans-serif; }
        html, body { height: 100%; overflow: hidden; }
        body { background-color: var(--bg-color); color: var(--text-color); transition: background-color 0.3s, color 0.3s; display: flex; }
        .sidebar { width: 260px; background-color: var(--sidebar-bg-color); border-right: 1px solid var(--border-color); display: flex; flex-direction: column; height: 100%; flex-shrink: 0; transition: background-color 0.3s, border-color 0.3s; }
        .main-wrapper { flex-grow: 1; height: 100%; overflow-y: auto; }
        .main-content { padding: 24px; }
        .sidebar-header { display: flex; align-items: center; padding: 0 24px; height: 64px; border-bottom: 1px solid var(--border-color); flex-shrink: 0; }
        .sidebar-logo { font-size: 22px; font-weight: 700; color: var(--primary-color); }
        .sidebar-nav { flex-grow: 1; padding: 16px 0; }
        .nav-list { list-style: none; }
        .nav-item a { display: flex; align-items: center; gap: 12px; padding: 12px 24px; margin: 4px 16px; border-radius: 8px; color: var(--text-color-secondary); text-decoration: none; font-weight: 500; transition: background-color 0.2s, color 0.2s; }
        .nav-item a:hover { background-color: var(--primary-color-light); color: var(--primary-color); }
        .nav-item.active a { background-color: var(--primary-color-light); color: var(--primary-color); font-weight: 600; }
        .nav-icon { width: 20px; height: 20px; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .page-title { font-size: 24px; font-weight: 700; }
        .header-actions { display: flex; align-items: center; gap: 16px; }
        .theme-toggle { background-color: var(--icon-hover-bg); border: none; border-radius: 50%; width: 36px; height: 36px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: var(--icon-color); transition: background-color 0.2s; }
        .theme-toggle .icon { width: 20px; height: 20px; }
        #moon-icon { display: none; }
        .card { background-color: var(--card-bg-color); border-radius: 12px; margin-bottom: 24px; box-shadow: 0 4px 12px var(--shadow-color); border: 1px solid var(--border-color); overflow: hidden; transition: background-color 0.3s, border-color 0.3s; }
        .card-header { display: flex; justify-content: space-between; align-items: center; padding: 16px 24px; border-bottom: 1px solid var(--border-color); }
        .card-title { font-size: 17px; font-weight: 600; color: var(--text-color); }
        .card-content { padding: 24px; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { text-align: left; padding: 12px 16px; border-bottom: 1px solid var(--border-color); font-size: 14px; }
        .table th { color: var(--text-color-secondary); font-weight: 500; }
        .search-form { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px; }
        .form-group { display: flex; flex-direction: column; gap: 6px; }
        .form-group label { font-size: 14px; color: var(--text-color-secondary); font-weight: 500; }
        .form-group input, .form-group select { padding: 8px 12px; border-radius: 6px; border: 1px solid var(--border-color); background-color: var(--card-bg-color); color: var(--text-color); }
        .form-actions { display: flex; gap: 12px; align-items: flex-end; }
        .btn-primary { background-color: var(--primary-color); color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; }
        .btn-secondary { background-color: var(--icon-hover-bg); color: var(--text-color); border: 1px solid var(--border-color); padding: 8px 16px; border-radius: 6px; cursor: pointer; }
        .status-badge { padding: 4px 10px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-success { background-color: rgba(76, 217, 100, 0.1); color: #4cd964; }
        .status-fail { background-color: rgba(255, 59, 48, 0.1); color: #ff3b30; }
        .status-pending { background-color: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .status-reviewing { background-color: rgba(54, 162, 235, 0.1); color: #36a2eb; }
        .status-completed { background-color: rgba(40, 167, 69, 0.1); color: #28a745; }

        /* 审查工作台样式 */
        .review-dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px; }
        .review-stat-card { background: linear-gradient(135deg, var(--primary-color), #4f46e5); color: white; padding: 20px; border-radius: 12px; text-align: center; }
        .review-stat-card.pending { background: linear-gradient(135deg, #ffc107, #fd7e14); }
        .review-stat-card.reviewing { background: linear-gradient(135deg, #36a2eb, #17a2b8); }
        .review-stat-card.completed { background: linear-gradient(135deg, #28a745, #20c997); }
        .review-stat-value { font-size: 28px; font-weight: 700; margin-bottom: 4px; }
        .review-stat-label { font-size: 14px; opacity: 0.9; }

        /* 案例卡片样式 */
        .case-card { border: 1px solid var(--border-color); border-radius: 8px; padding: 16px; margin-bottom: 12px; transition: border-color 0.2s, box-shadow 0.2s; }
        .case-card:hover { border-color: var(--primary-color); box-shadow: 0 2px 8px rgba(45, 120, 244, 0.1); }
        .case-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px; }
        .case-info { flex: 1; }
        .case-id { font-weight: 600; color: var(--text-color); margin-bottom: 4px; }
        .case-time { font-size: 12px; color: var(--text-color-secondary); }
        .case-priority { padding: 2px 8px; border-radius: 4px; font-size: 11px; font-weight: 500; }
        .priority-high { background-color: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .priority-medium { background-color: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .priority-low { background-color: rgba(40, 167, 69, 0.1); color: #28a745; }
        .status-pending { background-color: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .status-reviewing { background-color: rgba(54, 162, 235, 0.1); color: #36a2eb; }
        .status-completed { background-color: rgba(40, 167, 69, 0.1); color: #28a745; }

        /* 审查工作台样式 */
        .review-dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px; }
        .review-stat-card { background: linear-gradient(135deg, var(--primary-color), #4f46e5); color: white; padding: 20px; border-radius: 12px; text-align: center; }
        .review-stat-card.pending { background: linear-gradient(135deg, #ffc107, #fd7e14); }
        .review-stat-card.reviewing { background: linear-gradient(135deg, #36a2eb, #17a2b8); }
        .review-stat-card.completed { background: linear-gradient(135deg, #28a745, #20c997); }
        .review-stat-value { font-size: 28px; font-weight: 700; margin-bottom: 4px; }
        .review-stat-label { font-size: 14px; opacity: 0.9; }

        /* 案例卡片样式 */
        .case-card { border: 1px solid var(--border-color); border-radius: 8px; padding: 16px; margin-bottom: 12px; transition: border-color 0.2s, box-shadow 0.2s; }
        .case-card:hover { border-color: var(--primary-color); box-shadow: 0 2px 8px rgba(45, 120, 244, 0.1); }
        .case-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px; }
        .case-info { flex: 1; }
        .case-id { font-weight: 600; color: var(--text-color); margin-bottom: 4px; }
        .case-time { font-size: 12px; color: var(--text-color-secondary); }
        .case-priority { padding: 2px 8px; border-radius: 4px; font-size: 11px; font-weight: 500; }
        .priority-high { background-color: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .priority-medium { background-color: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .priority-low { background-color: rgba(40, 167, 69, 0.1); color: #28a745; }

        .case-details { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 12px; margin-bottom: 12px; }
        .case-detail-item { font-size: 13px; }
        .case-detail-label { color: var(--text-color-secondary); margin-bottom: 2px; }
        .case-detail-value { color: var(--text-color); font-weight: 500; }

        .case-actions { display: flex; gap: 8px; justify-content: flex-end; }
        .btn-sm { padding: 6px 12px; font-size: 12px; }

        /* 风险标签 */
        .risk-badge { padding: 3px 8px; border-radius: 12px; font-size: 11px; font-weight: 500; }
        .risk-high { background-color: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .risk-medium { background-color: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .risk-low { background-color: rgba(40, 167, 69, 0.1); color: #28a745; }

        /* 标签页样式 */
        .tab-nav { display: flex; border-bottom: 1px solid var(--border-color); margin-bottom: 20px; }
        .tab-item { padding: 12px 20px; cursor: pointer; border-bottom: 2px solid transparent; color: var(--text-color-secondary); font-weight: 500; transition: all 0.2s; }
        .tab-item.active { color: var(--primary-color); border-bottom-color: var(--primary-color); }
        .tab-item:hover { color: var(--primary-color); }

        .tab-content { display: none; }
        .tab-content.active { display: block; }

        /* 分页样式 */
        .pagination { display: flex; justify-content: center; align-items: center; gap: 8px; margin-top: 20px; }
        .pagination-btn { padding: 8px 12px; border: 1px solid var(--border-color); background: var(--card-bg-color); color: var(--text-color); border-radius: 6px; cursor: pointer; transition: all 0.2s; }
        .pagination-btn:hover { border-color: var(--primary-color); color: var(--primary-color); }
        .pagination-btn.active { background: var(--primary-color); color: white; border-color: var(--primary-color); }
        .pagination-info { color: var(--text-color-secondary); font-size: 14px; margin: 0 16px; }

        /* 黑名单操作样式 */
        .blacklist-actions { margin-top: 24px; padding: 20px; background: linear-gradient(135deg, rgba(220, 53, 69, 0.05), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(220, 53, 69, 0.2); border-radius: 8px; }
        .blacklist-title { margin-bottom: 16px; color: #dc3545; font-weight: 600; font-size: 16px; display: flex; align-items: center; gap: 8px; }
        .blacklist-options { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px; }
        .blacklist-option { padding: 16px; background: var(--card-bg-color); border: 1px solid var(--border-color); border-radius: 8px; transition: all 0.2s; }
        .blacklist-option:hover { border-color: #dc3545; box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1); }
        .blacklist-option-title { font-weight: 600; color: var(--text-color); margin-bottom: 8px; display: flex; align-items: center; gap: 8px; }
        .blacklist-option-desc { font-size: 13px; color: var(--text-color-secondary); margin-bottom: 12px; line-height: 1.4; }
        .blacklist-option-value { font-family: 'Courier New', monospace; font-size: 12px; background: var(--bg-color); padding: 8px; border-radius: 4px; margin-bottom: 12px; word-break: break-all; }
        .blacklist-btn { width: 100%; padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: 500; transition: all 0.2s; }
        .blacklist-btn:hover { background: #c82333; transform: translateY(-1px); }
        .blacklist-btn:active { transform: translateY(0); }
    </style>
</head>
<body data-theme="light">

    <aside class="sidebar">
        <div class="sidebar-header"><h1 class="sidebar-logo">SSID Platform</h1></div>
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item"><a href="dashboard.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path></svg><span>首页</span></a></li>
                <li class="nav-item"><a href="configuration.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg><span>配置管理</span></a></li>
                <li class="nav-item"><a href="analytics.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg><span>数据统计</span></a></li>
                <li class="nav-item"><a href="risk-management.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg><span>风险管控</span></a></li>
                <li class="nav-item active"><a href="case-audit.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg><span>案例审查</span></a></li>
                <li class="nav-item"><a href="feature-sets.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path></svg><span>特征库管理</span></a></li>
                <li class="nav-item"><a href="distribution.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path></svg><span>资源分发</span></a></li>
                <li class="nav-item"><a href="settings.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 16v-2m8-8h2M4 12H2m15.364 6.364l-1.414-1.414M6.05 6.05L4.636 4.636m12.728 12.728L15.95 15.95M6.05 17.95l1.414-1.414M12 18a6 6 0 100-12 6 6 0 000 12z"></path></svg><span>系统管理</span></a></li>
            </ul>
        </nav>
    </aside>

    <div class="main-wrapper">
        <main class="main-content">
            <header class="page-header">
                <h1 class="page-title">案例审查中心</h1>
                <div class="header-actions">
                    <button class="theme-toggle" id="theme-toggle-btn" title="切换亮/暗模式"><svg id="sun-icon" class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path></svg><svg id="moon-icon" class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path></svg></button>
                </div>
            </header>

            <!-- 审查统计概览 -->
            <div class="review-dashboard">
                <div class="review-stat-card">
                    <div class="review-stat-value">12,847</div>
                    <div class="review-stat-label">今日核验总数</div>
                </div>
                <div class="review-stat-card completed">
                    <div class="review-stat-value">11,079</div>
                    <div class="review-stat-label">通过核验</div>
                </div>
                <div class="review-stat-card pending">
                    <div class="review-stat-value">856</div>
                    <div class="review-stat-label">活体检测拦截</div>
                </div>
                <div class="review-stat-card reviewing">
                    <div class="review-stat-value">342</div>
                    <div class="review-stat-label">深伪检测拦截</div>
                </div>
                <div class="review-stat-card" style="background: linear-gradient(135deg, #fd7e14, #e83e8c);">
                    <div class="review-stat-value">228</div>
                    <div class="review-stat-label">相似背景拦截</div>
                </div>
                <div class="review-stat-card" style="background: linear-gradient(135deg, #6f42c1, #e83e8c);">
                    <div class="review-stat-value">342</div>
                    <div class="review-stat-label">风控拦截</div>
                </div>
            </div>

            <!-- 筛选条件 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">核验记录筛选</h2>
                </div>
                <div class="card-content">
                    <form class="search-form">
                        <div class="form-group">
                            <label for="user-id">用户ID</label>
                            <input type="text" id="user-id" placeholder="输入用户ID">
                        </div>
                        <div class="form-group">
                            <label for="request-id">请求流水号</label>
                            <input type="text" id="request-id" placeholder="输入请求流水号">
                        </div>
                        <div class="form-group">
                            <label for="verification-result">核验结果</label>
                            <select id="verification-result">
                                <option value="">全部结果</option>
                                <option value="success">通过</option>
                                <option value="liveness_block">活体检测拦截</option>
                                <option value="deepfake_block">深伪检测拦截</option>
                                <option value="background_block">相似背景拦截</option>
                                <option value="quality_block">图像质量拦截</option>
                                <option value="similarity_block">相似度拦截</option>
                                <option value="risk_block">风控拦截</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="channel">渠道</label>
                            <select id="channel">
                                <option value="">全部渠道</option>
                                <option value="mobile_bank">手机银行</option>
                                <option value="web_bank">网上银行</option>
                                <option value="mini_program">小程序</option>
                                <option value="app">移动应用</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="scenario">业务场景</label>
                            <select id="scenario">
                                <option value="">全部场景</option>
                                <option value="login">用户登录</option>
                                <option value="register">用户注册</option>
                                <option value="transfer">大额转账</option>
                                <option value="payment">支付确认</option>
                                <option value="modify">信息修改</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="risk-level">风险等级</label>
                            <select id="risk-level">
                                <option value="">全部等级</option>
                                <option value="high">高风险</option>
                                <option value="medium">中风险</option>
                                <option value="low">低风险</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="date-start">开始时间</label>
                            <input type="datetime-local" id="date-start">
                        </div>
                        <div class="form-group">
                            <label for="date-end">结束时间</label>
                            <input type="datetime-local" id="date-end">
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn-primary" onclick="searchRecords()">搜索</button>
                            <button type="button" class="btn-secondary" onclick="resetSearch()">重置</button>
                            <button type="button" class="btn-secondary" onclick="exportRecords()">导出</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 核验记录列表 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">核验记录</h2>
                    <div style="display: flex; gap: 12px; align-items: center;">
                        <span style="color: var(--text-color-secondary); font-size: 14px;">
                            共找到 <strong id="total-records">1,247</strong> 条记录
                        </span>
                        <button class="btn-secondary" onclick="refreshRecords()">刷新</button>
                    </div>
                </div>
                <div class="card-content" style="padding: 0;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>核验时间</th>
                                <th>用户ID</th>
                                <th>请求流水号</th>
                                <th>渠道/场景</th>
                                <th>核验结果</th>
                                <th>拦截类型</th>
                                <th>风险等级</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-01-25 11:23:45</td>
                                <td>user_11****34</td>
                                <td>req_abc123xyz</td>
                                <td>手机银行/大额转账</td>
                                <td><span class="status-badge status-fail">拦截</span></td>
                                <td><span class="status-badge status-pending">活体检测拦截</span></td>
                                <td><span class="risk-badge risk-high">高风险</span></td>
                                <td>
                                    <button class="btn-primary btn-sm" onclick="viewRecordDetail('req_abc123xyz')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2024-01-25 11:22:18</td>
                                <td>user_9a****f1</td>
                                <td>req_def456uvw</td>
                                <td>小程序/用户登录</td>
                                <td><span class="status-badge status-fail">拦截</span></td>
                                <td><span class="status-badge" style="background-color: rgba(253, 126, 20, 0.1); color: #fd7e14;">深伪检测拦截</span></td>
                                <td><span class="risk-badge risk-high">高风险</span></td>
                                <td>
                                    <button class="btn-primary btn-sm" onclick="viewRecordDetail('req_def456uvw')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2024-01-25 11:21:02</td>
                                <td>user_c3****88</td>
                                <td>req_ghi789rst</td>
                                <td>网上银行/用户注册</td>
                                <td><span class="status-badge status-success">通过</span></td>
                                <td><span style="color: var(--text-color-secondary);">-</span></td>
                                <td><span class="risk-badge risk-low">低风险</span></td>
                                <td>
                                    <button class="btn-primary btn-sm" onclick="viewRecordDetail('req_ghi789rst')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2024-01-25 11:20:15</td>
                                <td>user_d5****22</td>
                                <td>req_jkl012opq</td>
                                <td>移动应用/支付确认</td>
                                <td><span class="status-badge status-fail">拦截</span></td>
                                <td><span class="status-badge" style="background-color: rgba(232, 62, 140, 0.1); color: #e83e8c;">相似背景拦截</span></td>
                                <td><span class="risk-badge risk-medium">中风险</span></td>
                                <td>
                                    <button class="btn-primary btn-sm" onclick="viewRecordDetail('req_jkl012opq')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2024-01-25 11:19:33</td>
                                <td>user_e7****66</td>
                                <td>req_mno345pqr</td>
                                <td>手机银行/信息修改</td>
                                <td><span class="status-badge status-success">通过</span></td>
                                <td><span style="color: var(--text-color-secondary);">-</span></td>
                                <td><span class="risk-badge risk-low">低风险</span></td>
                                <td>
                                    <button class="btn-primary btn-sm" onclick="viewRecordDetail('req_mno345pqr')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2024-01-25 11:18:47</td>
                                <td>user_f9****11</td>
                                <td>req_stu678vwx</td>
                                <td>小程序/用户注册</td>
                                <td><span class="status-badge status-fail">拦截</span></td>
                                <td><span class="status-badge" style="background-color: rgba(111, 66, 193, 0.1); color: #6f42c1;">风控拦截</span></td>
                                <td><span class="risk-badge risk-medium">中风险</span></td>
                                <td>
                                    <button class="btn-primary btn-sm" onclick="viewRecordDetail('req_stu678vwx')">详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="pagination">
                    <button class="pagination-btn" onclick="changePage('prev')" id="prevBtn">上一页</button>
                    <button class="pagination-btn active" onclick="goToPage(1)">1</button>
                    <button class="pagination-btn" onclick="goToPage(2)">2</button>
                    <button class="pagination-btn" onclick="goToPage(3)">3</button>
                    <button class="pagination-btn" onclick="goToPage(4)">4</button>
                    <button class="pagination-btn" onclick="goToPage(5)">5</button>
                    <span class="pagination-info">共 <span id="totalRecords">1,247</span> 条记录，第 <span id="currentPage">1</span> 页，共 <span id="totalPages">125</span> 页</span>
                    <button class="pagination-btn" onclick="changePage('next')" id="nextBtn">下一页</button>
                </div>
            </div>

        </main>
    </div>

    <!-- 核验记录详情模态框 -->
    <div id="recordDetailModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1000;">
        <div class="modal-content" style="background: var(--card-bg-color); margin: 2% auto; padding: 0; width: 90%; max-width: 1200px; border-radius: 12px; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; padding: 20px 24px; border-bottom: 1px solid var(--border-color);">
                <h2 class="modal-title" style="margin: 0; font-size: 18px; font-weight: 600;">核验记录详情</h2>
                <button class="modal-close" onclick="closeRecordDetailModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: var(--text-color-secondary);">&times;</button>
            </div>
            <div class="modal-body" style="padding: 24px;" id="recordDetailContent">
                <!-- 详情内容将在这里动态加载 -->
            </div>
        </div>
    </div>
        </main>
    </div>

    <script>
        // 主题切换功能
        const themeToggleBtn = document.getElementById('theme-toggle-btn');
        const sunIcon = document.getElementById('sun-icon');
        const moonIcon = document.getElementById('moon-icon');
        const body = document.body;
        const applyTheme = (theme) => {
            body.setAttribute('data-theme', theme);
            if (theme === 'dark') { sunIcon.style.display = 'none'; moonIcon.style.display = 'block'; } else { sunIcon.style.display = 'block'; moonIcon.style.display = 'none'; }
        };
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        const defaultTheme = savedTheme || (prefersDark ? 'dark' : 'light');
        applyTheme(defaultTheme);
        themeToggleBtn.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            localStorage.setItem('theme', newTheme);
            applyTheme(newTheme);
        });

        // 搜索功能
        function searchRecords() {
            const userId = document.getElementById('user-id').value;
            const requestId = document.getElementById('request-id').value;
            const verificationResult = document.getElementById('verification-result').value;
            const channel = document.getElementById('channel').value;
            const scenario = document.getElementById('scenario').value;
            const riskLevel = document.getElementById('risk-level').value;
            const dateStart = document.getElementById('date-start').value;
            const dateEnd = document.getElementById('date-end').value;

            console.log('搜索条件:', {
                userId, requestId, verificationResult, channel, scenario, riskLevel, dateStart, dateEnd
            });

            alert('搜索功能已执行！\n' +
                  `用户ID: ${userId || '全部'}\n` +
                  `请求流水号: ${requestId || '全部'}\n` +
                  `核验结果: ${verificationResult || '全部'}\n` +
                  `渠道: ${channel || '全部'}\n` +
                  `场景: ${scenario || '全部'}\n` +
                  `风险等级: ${riskLevel || '全部'}`);
        }

        function resetSearch() {
            document.getElementById('user-id').value = '';
            document.getElementById('request-id').value = '';
            document.getElementById('verification-result').value = '';
            document.getElementById('channel').value = '';
            document.getElementById('scenario').value = '';
            document.getElementById('risk-level').value = '';
            document.getElementById('date-start').value = '';
            document.getElementById('date-end').value = '';
        }

        function exportRecords() {
            alert('导出功能\n\n将导出当前筛选条件下的所有核验记录，包括：\n- 基本信息\n- 核验结果\n- 风险详情\n- 设备信息');
        }

        function refreshRecords() {
            alert('刷新记录列表');
            // 这里可以重新加载数据
        }

        // 核验记录详情
        function viewRecordDetail(requestId) {
            // 模拟详情数据
            const recordDetails = {
                'req_abc123xyz': {
                    requestId: 'req_abc123xyz',
                    userId: 'user_11****34',
                    verificationTime: '2024-01-25 11:23:45',
                    channel: '手机银行',
                    scenario: '大额转账',
                    result: '拦截',
                    blockType: '算法拦截',
                    riskLevel: '高风险',
                    confidence: '95.8%',
                    deviceInfo: {
                        deviceId: 'ff4e8a2b...c1a0d5f8',
                        deviceType: 'Android模拟器',
                        osVersion: 'Android 9.0',
                        appVersion: '2.1.5',
                        ip: '*************',
                        location: '北京市朝阳区',
                        userAgent: 'Mozilla/5.0 (Linux; Android 9; SM-G960F) AppleWebKit/537.36'
                    },
                    algorithmResult: {
                        faceDetection: {
                            status: '检测到人脸',
                            confidence: '99.2%',
                            result: 'PASS'
                        },
                        liveness: {
                            status: '活体检测失败',
                            confidence: '15.3%',
                            result: 'FAIL',
                            reason: '检测到静态图像特征'
                        },
                        deepfake: {
                            status: '深伪检测通过',
                            confidence: '92.8%',
                            result: 'PASS'
                        },
                        backgroundSimilarity: {
                            status: '相似背景检测通过',
                            confidence: '88.5%',
                            result: 'PASS'
                        },
                        similarity: {
                            status: '相似度检测通过',
                            score: '98.5%',
                            result: 'PASS'
                        },
                        quality: {
                            status: '图像质量优秀',
                            score: '95.2%',
                            result: 'PASS'
                        },
                        finalResult: 'FAIL',
                        blockReason: '活体检测失败',
                        riskFeatures: ['静态图像', '光照异常', '纹理缺失']
                    },
                    riskControl: {
                        triggeredRules: ['模拟器检测拦截'],
                        riskScore: 85,
                        responseAction: '直接拒绝'
                    },
                    logs: [
                        { time: '11:23:45.123', level: 'INFO', message: '接收到核验请求' },
                        { time: '11:23:45.234', level: 'INFO', message: '开始人脸检测' },
                        { time: '11:23:45.456', level: 'WARN', message: '检测到模拟器特征' },
                        { time: '11:23:45.567', level: 'ERROR', message: '触发风险规则: 模拟器检测拦截' },
                        { time: '11:23:45.678', level: 'INFO', message: '执行响应动作: 直接拒绝' }
                    ],
                    images: [
                        { type: '原始图像', url: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=' },
                        { type: '检测结果', url: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=' }
                    ]
                }
            };

            const record = recordDetails[requestId] || recordDetails['req_abc123xyz'];

            const detailHtml = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                    <div>
                        <h4 style="margin-bottom: 16px; color: var(--text-color); border-bottom: 1px solid var(--border-color); padding-bottom: 8px;">基本信息</h4>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                            <div><strong>请求流水号:</strong> ${record.requestId}</div>
                            <div><strong>用户ID:</strong> ${record.userId}</div>
                            <div><strong>核验时间:</strong> ${record.verificationTime}</div>
                            <div><strong>渠道:</strong> ${record.channel}</div>
                            <div><strong>业务场景:</strong> ${record.scenario}</div>
                            <div><strong>核验结果:</strong> <span style="color: #dc3545; font-weight: 600;">${record.result}</span></div>
                            <div><strong>拦截类型:</strong> <span style="color: #ffc107; font-weight: 600;">${record.blockType}</span></div>
                            <div><strong>风险等级:</strong> <span class="risk-badge risk-high">${record.riskLevel}</span></div>
                            <div><strong>置信度:</strong> ${record.confidence}</div>
                        </div>
                    </div>
                    <div>
                        <h4 style="margin-bottom: 16px; color: var(--text-color); border-bottom: 1px solid var(--border-color); padding-bottom: 8px;">设备信息</h4>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                            <div><strong>设备ID:</strong> ${record.deviceInfo.deviceId}</div>
                            <div><strong>设备类型:</strong> ${record.deviceInfo.deviceType}</div>
                            <div><strong>系统版本:</strong> ${record.deviceInfo.osVersion}</div>
                            <div><strong>应用版本:</strong> ${record.deviceInfo.appVersion}</div>
                            <div><strong>IP地址:</strong> ${record.deviceInfo.ip}</div>
                            <div><strong>地理位置:</strong> ${record.deviceInfo.location}</div>
                            <div><strong>User Agent:</strong> <span style="font-size: 12px; word-break: break-all;">${record.deviceInfo.userAgent}</span></div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 24px;">
                    <h4 style="margin-bottom: 16px; color: var(--text-color); border-bottom: 1px solid var(--border-color); padding-bottom: 8px;">算法检测结果</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
                        <div style="background-color: var(--bg-color); padding: 16px; border-radius: 8px; border-left: 4px solid ${record.algorithmResult.faceDetection.result === 'PASS' ? '#28a745' : '#dc3545'};">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <strong>人脸检测</strong>
                                <span style="color: ${record.algorithmResult.faceDetection.result === 'PASS' ? '#28a745' : '#dc3545'}; font-weight: 600;">${record.algorithmResult.faceDetection.result}</span>
                            </div>
                            <div style="font-size: 13px; color: var(--text-color-secondary); margin-bottom: 4px;">${record.algorithmResult.faceDetection.status}</div>
                            <div style="font-size: 12px; color: var(--text-color-secondary);">置信度: ${record.algorithmResult.faceDetection.confidence}</div>
                        </div>

                        <div style="background-color: var(--bg-color); padding: 16px; border-radius: 8px; border-left: 4px solid ${record.algorithmResult.liveness.result === 'PASS' ? '#28a745' : '#dc3545'};">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <strong>活体检测</strong>
                                <span style="color: ${record.algorithmResult.liveness.result === 'PASS' ? '#28a745' : '#dc3545'}; font-weight: 600;">${record.algorithmResult.liveness.result}</span>
                            </div>
                            <div style="font-size: 13px; color: var(--text-color-secondary); margin-bottom: 4px;">${record.algorithmResult.liveness.status}</div>
                            <div style="font-size: 12px; color: var(--text-color-secondary); margin-bottom: 4px;">置信度: ${record.algorithmResult.liveness.confidence}</div>
                            ${record.algorithmResult.liveness.reason ? `<div style="font-size: 12px; color: #dc3545;">原因: ${record.algorithmResult.liveness.reason}</div>` : ''}
                        </div>

                        <div style="background-color: var(--bg-color); padding: 16px; border-radius: 8px; border-left: 4px solid ${record.algorithmResult.deepfake.result === 'PASS' ? '#28a745' : '#dc3545'};">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <strong>深伪检测</strong>
                                <span style="color: ${record.algorithmResult.deepfake.result === 'PASS' ? '#28a745' : '#dc3545'}; font-weight: 600;">${record.algorithmResult.deepfake.result}</span>
                            </div>
                            <div style="font-size: 13px; color: var(--text-color-secondary); margin-bottom: 4px;">${record.algorithmResult.deepfake.status}</div>
                            <div style="font-size: 12px; color: var(--text-color-secondary);">置信度: ${record.algorithmResult.deepfake.confidence}</div>
                        </div>

                        <div style="background-color: var(--bg-color); padding: 16px; border-radius: 8px; border-left: 4px solid ${record.algorithmResult.backgroundSimilarity.result === 'PASS' ? '#28a745' : '#dc3545'};">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <strong>相似背景检测</strong>
                                <span style="color: ${record.algorithmResult.backgroundSimilarity.result === 'PASS' ? '#28a745' : '#dc3545'}; font-weight: 600;">${record.algorithmResult.backgroundSimilarity.result}</span>
                            </div>
                            <div style="font-size: 13px; color: var(--text-color-secondary); margin-bottom: 4px;">${record.algorithmResult.backgroundSimilarity.status}</div>
                            <div style="font-size: 12px; color: var(--text-color-secondary);">置信度: ${record.algorithmResult.backgroundSimilarity.confidence}</div>
                        </div>

                        <div style="background-color: var(--bg-color); padding: 16px; border-radius: 8px; border-left: 4px solid ${record.algorithmResult.similarity.result === 'PASS' ? '#28a745' : '#dc3545'};">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <strong>相似度检测</strong>
                                <span style="color: ${record.algorithmResult.similarity.result === 'PASS' ? '#28a745' : '#dc3545'}; font-weight: 600;">${record.algorithmResult.similarity.result}</span>
                            </div>
                            <div style="font-size: 13px; color: var(--text-color-secondary); margin-bottom: 4px;">${record.algorithmResult.similarity.status}</div>
                            <div style="font-size: 12px; color: var(--text-color-secondary);">相似度: ${record.algorithmResult.similarity.score}</div>
                        </div>

                        <div style="background-color: var(--bg-color); padding: 16px; border-radius: 8px; border-left: 4px solid ${record.algorithmResult.quality.result === 'PASS' ? '#28a745' : '#dc3545'};">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <strong>图像质量检测</strong>
                                <span style="color: ${record.algorithmResult.quality.result === 'PASS' ? '#28a745' : '#dc3545'}; font-weight: 600;">${record.algorithmResult.quality.result}</span>
                            </div>
                            <div style="font-size: 13px; color: var(--text-color-secondary); margin-bottom: 4px;">${record.algorithmResult.quality.status}</div>
                            <div style="font-size: 12px; color: var(--text-color-secondary);">质量评分: ${record.algorithmResult.quality.score}</div>
                        </div>
                    </div>

                    <div style="margin-top: 16px; padding: 16px; background-color: ${record.algorithmResult.finalResult === 'PASS' ? 'rgba(40, 167, 69, 0.1)' : 'rgba(220, 53, 69, 0.1)'}; border-radius: 8px; border: 1px solid ${record.algorithmResult.finalResult === 'PASS' ? '#28a745' : '#dc3545'};">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <strong style="color: ${record.algorithmResult.finalResult === 'PASS' ? '#28a745' : '#dc3545'};">最终检测结果</strong>
                            <span style="color: ${record.algorithmResult.finalResult === 'PASS' ? '#28a745' : '#dc3545'}; font-weight: 600; font-size: 16px;">${record.algorithmResult.finalResult}</span>
                        </div>
                        ${record.algorithmResult.blockReason ? `<div style="margin-bottom: 12px;"><strong>拦截原因:</strong> <span style="color: #dc3545;">${record.algorithmResult.blockReason}</span></div>` : ''}
                        <div><strong>检测到的风险特征:</strong> ${record.algorithmResult.riskFeatures.join(', ')}</div>
                    </div>
                </div>

                <div style="margin-top: 24px;">
                    <h4 style="margin-bottom: 16px; color: var(--text-color); border-bottom: 1px solid var(--border-color); padding-bottom: 8px;">风控处理</h4>
                    <div style="background-color: var(--bg-color); padding: 16px; border-radius: 8px;">
                        <div style="margin-bottom: 12px;"><strong>触发规则:</strong> ${record.riskControl.triggeredRules.join(', ')}</div>
                        <div style="margin-bottom: 12px;"><strong>风险评分:</strong> ${record.riskControl.riskScore}/100</div>
                        <div><strong>响应动作:</strong> ${record.riskControl.responseAction}</div>
                    </div>
                </div>

                <div style="margin-top: 24px;">
                    <h4 style="margin-bottom: 16px; color: var(--text-color); border-bottom: 1px solid var(--border-color); padding-bottom: 8px;">图像信息</h4>
                    <div style="display: flex; gap: 16px;">
                        ${record.images.map(img => `
                            <div style="text-align: center;">
                                <div style="margin-bottom: 8px; font-weight: 500;">${img.type}</div>
                                <img src="${img.url}" alt="${img.type}" style="width: 150px; height: 150px; object-fit: cover; border-radius: 8px; border: 1px solid var(--border-color);">
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div style="margin-top: 24px;">
                    <h4 style="margin-bottom: 16px; color: var(--text-color); border-bottom: 1px solid var(--border-color); padding-bottom: 8px;">处理日志</h4>
                    <div style="background-color: #1e1e1e; color: #f8f8f2; padding: 16px; border-radius: 8px; font-family: 'Courier New', monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
                        ${record.logs.map(log => `
                            <div style="margin-bottom: 4px;">
                                <span style="color: #6272a4;">[${log.time}]</span>
                                <span style="color: ${log.level === 'ERROR' ? '#ff5555' : log.level === 'WARN' ? '#f1fa8c' : '#50fa7b'};">[${log.level}]</span>
                                <span>${log.message}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="blacklist-actions">
                    <div class="blacklist-title">
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        黑名单操作
                    </div>
                    <div class="blacklist-options">
                        <div class="blacklist-option">
                            <div class="blacklist-option-title">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                                IP地址黑名单
                            </div>
                            <div class="blacklist-option-desc">
                                将此IP地址加入黑名单，阻止来自该IP的所有核验请求
                            </div>
                            <div class="blacklist-option-value">${record.deviceInfo.ip}</div>
                            <button class="blacklist-btn" onclick="addToBlacklist('ip', '${record.deviceInfo.ip}', '${record.requestId}')">
                                加入IP黑名单
                            </button>
                        </div>

                        <div class="blacklist-option">
                            <div class="blacklist-option-title">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M17 2H7c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 18H7V4h10v16z"/>
                                </svg>
                                设备指纹黑名单
                            </div>
                            <div class="blacklist-option-desc">
                                将此设备指纹加入黑名单，阻止该设备的所有核验请求
                            </div>
                            <div class="blacklist-option-value">${record.deviceInfo.deviceId}</div>
                            <button class="blacklist-btn" onclick="addToBlacklist('device', '${record.deviceInfo.deviceId}', '${record.requestId}')">
                                加入设备黑名单
                            </button>
                        </div>

                        <div class="blacklist-option">
                            <div class="blacklist-option-title">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                </svg>
                                用户ID黑名单
                            </div>
                            <div class="blacklist-option-desc">
                                将此用户ID加入黑名单，阻止该用户的所有核验请求
                            </div>
                            <div class="blacklist-option-value">${record.userId}</div>
                            <button class="blacklist-btn" onclick="addToBlacklist('user', '${record.userId}', '${record.requestId}')">
                                加入用户黑名单
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('recordDetailContent').innerHTML = detailHtml;
            document.getElementById('recordDetailModal').style.display = 'block';
        }

        function closeRecordDetailModal() {
            document.getElementById('recordDetailModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        document.getElementById('recordDetailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRecordDetailModal();
            }
        });

        // 黑名单操作功能
        function addToBlacklist(type, value, requestId) {
            const typeNames = {
                'ip': 'IP地址',
                'device': '设备指纹',
                'user': '用户ID'
            };

            const typeName = typeNames[type];
            const confirmMessage = `确认要将以下${typeName}加入黑名单吗？\n\n${typeName}: ${value}\n\n加入黑名单后，该${typeName}的所有核验请求都将被自动拦截。`;

            if (confirm(confirmMessage)) {
                // 模拟API调用
                const loadingMessage = `正在将${typeName}加入黑名单...`;

                // 显示加载状态
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '处理中...';
                button.disabled = true;

                // 模拟API请求
                setTimeout(() => {
                    // 恢复按钮状态
                    button.textContent = originalText;
                    button.disabled = false;

                    // 显示成功消息
                    alert(`✅ 黑名单操作成功！\n\n${typeName}: ${value}\n已成功加入黑名单\n\n相关信息：\n- 请求流水号: ${requestId}\n- 操作时间: ${new Date().toLocaleString()}\n- 操作员: 当前用户\n\n该${typeName}的后续核验请求将被自动拦截。`);

                    // 可以在这里调用实际的API
                    console.log('黑名单操作:', {
                        type: type,
                        value: value,
                        requestId: requestId,
                        timestamp: new Date().toISOString(),
                        operator: '当前用户'
                    });

                    // 可以选择关闭模态框或刷新数据
                    // closeRecordDetailModal();

                }, 1500);
            }
        }

        // 批量黑名单操作（可扩展功能）
        function batchAddToBlacklist(records, type) {
            const typeNames = {
                'ip': 'IP地址',
                'device': '设备指纹',
                'user': '用户ID'
            };

            const typeName = typeNames[type];
            const count = records.length;

            if (confirm(`确认要将 ${count} 个${typeName}批量加入黑名单吗？\n\n此操作不可撤销，请谨慎操作。`)) {
                console.log(`批量加入${typeName}黑名单:`, records);
                alert(`✅ 批量黑名单操作成功！\n\n已将 ${count} 个${typeName}加入黑名单。`);
            }
        }

        // 分页功能
        function changePage(direction) {
            console.log(`切换页面: ${direction}`);
        }

        function goToPage(page) {
            console.log(`跳转到第${page}页`);
        }
    </script>
</body>
</html>