# 策略配置表单组件设计文档

## 组件概述

PolicyConfigForm 是策略配置管理的核心组件，提供完整的核验策略创建和编辑功能。该组件采用模块化设计，包含基本信息、活体模式配置、阈值参数、质量控制和数据返回等配置区块。

## 组件结构

```
PolicyConfigForm
├── BasicInfoSection (基本信息区块)
│   ├── PolicyNameInput (策略名称输入)
│   ├── PolicyDescriptionTextarea (策略描述)
│   ├── ChannelSelector (渠道选择器)
│   └── ScenarioSelector (场景选择器)
├── LivenessModeSection (活体模式配置区块)
│   ├── ModeSelector (模式选择器)
│   └── InteractiveConfigPanel (交互活体配置面板)
├── ThresholdConfigSection (阈值参数配置区块)
│   ├── LivenessThreshold (活体分数阈值)
│   ├── ComparisonThreshold (1:1比对阈值)
│   └── DeepfakeThreshold (Deepfake阈值)
├── QualityControlSection (人脸质量控制区块)
│   ├── QualityToggleGroup (质量控制开关组)
│   └── QualityParameterConfig (质量参数配置)
└── DataReturnSection (数据返回配置区块)
    └── ReturnOptionToggles (返回选项开关)
```

## 详细功能设计

### 1. 基本信息区块 (BasicInfoSection)

#### 1.1 策略名称输入 (PolicyNameInput)
- **功能**: 输入策略名称，支持实时验证
- **验证规则**: 
  - 必填字段
  - 长度限制：2-50个字符
  - 不能与现有策略重名
- **交互**: 失焦时进行唯一性验证

#### 1.2 渠道/场景选择器 (ChannelSelector/ScenarioSelector)
- **功能**: 多选下拉框，支持添加和移除
- **特性**:
  - 支持搜索过滤
  - 已选项目显示为标签形式
  - 支持批量选择和清空
- **数据源**: 从渠道场景管理API获取

### 2. 活体模式配置区块 (LivenessModeSection)

#### 2.1 模式选择器 (ModeSelector)
- **设计**: 卡片式选择器，每种模式一个卡片
- **模式类型**:
  - 静默活体：无需用户配合
  - 交互活体：需要用户完成指定动作
  - 炫彩活体：通过屏幕变色检测
- **交互**: 点击卡片选择，选中状态高亮显示

#### 2.2 交互活体配置面板 (InteractiveConfigPanel)
- **显示条件**: 仅在选择交互活体时显示
- **配置项**:
  - 动作序列：支持多选（眨眼、张嘴、转头等）
  - 动作数量：1-5个，建议1-3个
  - 超时时间：10-60秒，默认30秒
- **验证**: 动作数量不能超过选择的动作种类

### 3. 阈值参数配置区块 (ThresholdConfigSection)

#### 3.1 阈值配置组件设计
- **UI组件**: 滑块 + 数值输入框组合
- **实时预览**: 显示当前阈值对应的通过率和误拒率
- **阈值类型**:
  - 活体分数阈值 (0-1)：影响活体检测严格程度
  - 1:1比对阈值 (0-1)：影响人脸比对准确度
  - Deepfake阈值 (0-1)：影响深度伪造检测敏感度

#### 3.2 预览算法
```javascript
// 活体阈值预览计算
function calculateLivenessPreview(threshold) {
    const passRate = Math.round((1 - threshold) * 100 + 80);
    const rejectRate = Math.round(threshold * 5);
    return { passRate, rejectRate };
}

// 比对阈值预览计算
function calculateComparisonPreview(threshold) {
    const passRate = Math.round((1 - threshold) * 100 + 85);
    const rejectRate = Math.round(threshold * 3);
    return { passRate, rejectRate };
}

// Deepfake阈值预览计算
function calculateDeepfakePreview(threshold) {
    const detectRate = Math.round(threshold * 100);
    const falseRate = Math.round((1 - threshold) * 5);
    return { detectRate, falseRate };
}
```

### 4. 人脸质量控制区块 (QualityControlSection)

#### 4.1 质量控制开关组
- **控制项**:
  - 允许闭眼：是否接受闭眼状态的人脸
  - 允许张嘴：是否接受张嘴状态的人脸
  - 允许面部遮挡：是否接受部分遮挡的人脸
  - 允许侧脸：是否接受非正面角度的人脸
- **UI设计**: 切换开关 + 说明文字
- **默认值**: 根据安全等级设置合理默认值

### 5. 数据返回配置区块 (DataReturnSection)

#### 5.1 返回选项配置
- **返回内容**:
  - 最佳人脸图：返回质量最高的人脸图片
  - 全过程视频：返回完整的核验过程视频
  - 处理日志：返回详细的处理步骤日志
  - 算法分数：返回各项算法的置信度分数
- **注意事项**: 返回内容越多，响应时间越长，需要权衡

## 版本控制功能

### 版本历史组件 (VersionHistoryPanel)

#### 功能特性
- **版本列表**: 显示所有历史版本，包含版本号、时间、修改说明
- **版本对比**: 支持任意两个版本的配置对比
- **一键回滚**: 支持回滚到任意历史版本
- **变更追踪**: 记录每次修改的具体内容

#### 版本对比界面设计
```html
<div class="version-comparison">
    <div class="comparison-header">
        <div class="version-info">v1.2 (当前版本)</div>
        <div class="vs-divider">VS</div>
        <div class="version-info">v1.1</div>
    </div>
    <div class="comparison-content">
        <div class="diff-section">
            <h4>活体模式配置</h4>
            <div class="diff-item">
                <span class="diff-label">活体分数阈值:</span>
                <span class="diff-old">0.75</span>
                <span class="diff-arrow">→</span>
                <span class="diff-new">0.80</span>
            </div>
        </div>
    </div>
</div>
```

## 表单验证

### 验证规则
```javascript
const validationRules = {
    policyName: {
        required: true,
        minLength: 2,
        maxLength: 50,
        unique: true
    },
    channels: {
        required: true,
        minItems: 1
    },
    scenarios: {
        required: true,
        minItems: 1
    },
    livenessThreshold: {
        required: true,
        min: 0,
        max: 1
    },
    interactiveActions: {
        requiredIf: 'livenessMode === "interactive"',
        minItems: 1,
        maxItems: 5
    }
};
```

### 错误处理
- **实时验证**: 在用户输入时进行基础验证
- **提交验证**: 在表单提交时进行完整验证
- **错误显示**: 在对应字段下方显示错误信息
- **错误汇总**: 在表单顶部显示所有错误的汇总

## 数据模型

### 策略配置数据结构
```typescript
interface PolicyConfig {
    id?: string;
    name: string;
    description?: string;
    channels: string[];
    scenarios: string[];
    livenessMode: 'silent' | 'interactive' | 'colorful';
    interactiveConfig?: {
        actions: string[];
        actionCount: number;
        timeout: number;
    };
    thresholds: {
        liveness: number;
        comparison: number;
        deepfake: number;
    };
    qualityControl: {
        allowClosedEyes: boolean;
        allowOpenMouth: boolean;
        allowOcclusion: boolean;
        allowSideface: boolean;
    };
    dataReturn: {
        bestFaceImage: boolean;
        fullVideo: boolean;
        processLogs: boolean;
        algorithmScores: boolean;
    };
    status: 'draft' | 'active' | 'inactive';
    version: number;
    createdAt: Date;
    updatedAt: Date;
}
```

## API接口设计

### 策略管理接口
```typescript
// 创建策略
POST /api/policies
Body: PolicyConfig

// 更新策略
PUT /api/policies/:id
Body: PolicyConfig

// 获取策略详情
GET /api/policies/:id

// 获取策略列表
GET /api/policies?status=active&page=1&limit=10

// 获取版本历史
GET /api/policies/:id/versions

// 版本回滚
POST /api/policies/:id/rollback
Body: { targetVersion: number }

// 策略复制
POST /api/policies/:id/duplicate
Body: { newName: string }
```

## 性能优化

### 1. 组件懒加载
- 版本历史面板按需加载
- 大型配置面板使用虚拟滚动

### 2. 数据缓存
- 渠道场景数据本地缓存
- 表单数据自动保存草稿

### 3. 用户体验优化
- 表单数据实时保存
- 网络异常时的离线编辑支持
- 长表单的分步骤引导

## 测试用例

### 单元测试
- 表单验证逻辑测试
- 阈值计算算法测试
- 数据转换函数测试

### 集成测试
- 完整的策略创建流程测试
- 版本控制功能测试
- 错误处理流程测试

### 用户体验测试
- 表单填写效率测试
- 错误提示清晰度测试
- 响应式布局适配测试