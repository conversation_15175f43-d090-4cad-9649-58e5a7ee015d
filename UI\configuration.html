<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSID Platform - 配置管理中心 (最终交付版)</title>
    <style>
        /* --- 基础与主题样式 --- */
        :root { --bg-color: #f6f8fb; --sidebar-bg-color: #ffffff; --card-bg-color: #ffffff; --text-color: #1e293b; --text-color-secondary: #64748b; --border-color: #e5eaf3; --primary-color: #2d78f4; --primary-color-light: #f0f6ff; --shadow-color: rgba(0, 0, 0, 0.06); --icon-color: #64748b; --icon-hover-bg: #f1f5f9; --success-color: #4cd964; --warning-color: #ff9500; --danger-color: #ff3b30; }
        * { margin: 0; padding: 0; box-sizing: border-box; font-family: "PingFang SC", "Microsoft YaHei", sans-serif; }
        html, body { height: 100%; overflow: hidden; }
        body { background-color: var(--bg-color); color: var(--text-color); display: flex; }

        /* --- 布局与组件 --- */
        .sidebar { width: 260px; background-color: var(--sidebar-bg-color); border-right: 1px solid var(--border-color); display: flex; flex-direction: column; height: 100%; flex-shrink: 0; }
        .sidebar-header { display: flex; align-items: center; padding: 0 24px; height: 64px; border-bottom: 1px solid var(--border-color); flex-shrink: 0; }
        .sidebar-logo { font-size: 22px; font-weight: 700; color: var(--primary-color); }
        .sidebar-nav { flex-grow: 1; padding: 16px 0; }
        .nav-list { list-style: none; }
        .nav-item a { display: flex; align-items: center; gap: 12px; padding: 12px 24px; margin: 4px 16px; border-radius: 8px; color: var(--text-color-secondary); text-decoration: none; font-weight: 500; transition: background-color 0.2s, color 0.2s; }
        .nav-item a:hover { background-color: var(--primary-color-light); color: var(--primary-color); }
        .nav-item.active a { background-color: var(--primary-color-light); color: var(--primary-color); font-weight: 600; }
        .nav-icon { width: 20px; height: 20px; }
        .sub-nav-list { list-style: none; margin-left: 16px; max-height: 0; overflow: hidden; transition: max-height 0.3s ease; }
        .nav-item.expanded .sub-nav-list { max-height: 200px; }
        .sub-nav-list .nav-item a { padding: 8px 24px; margin: 2px 16px; font-size: 13px; }
        .sub-nav-list .nav-item a:before { content: "•"; margin-right: 8px; color: var(--text-color-secondary); }
        .nav-item.has-submenu > a { position: relative; }
        .nav-item.has-submenu > a::after { content: "▶"; position: absolute; right: 16px; font-size: 12px; transition: transform 0.3s ease; }
        .nav-item.has-submenu.expanded > a::after { transform: rotate(90deg); }
        .main-wrapper { flex-grow: 1; height: 100%; display: flex; flex-direction: column; }
        .main-content { padding: 24px; flex-grow: 1; display: flex; flex-direction: column; overflow: hidden; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px; flex-shrink: 0; }
        .page-title { font-size: 24px; font-weight: 700; }
        .btn { padding: 8px 16px; border-radius: 6px; border: 1px solid transparent; font-size: 14px; cursor: pointer; font-weight: 500; transition: all 0.2s; display: inline-flex; align-items: center; gap: 6px; }
        .main-content { font-size: 14px; }
        .btn-sm { padding: 4px 10px; font-size: 12px; }
        .btn-primary { background-color: var(--primary-color); color: white; border-color: var(--primary-color); }
        .btn-secondary { background-color: var(--card-bg-color); color: var(--text-color); border-color: var(--border-color); }
        .btn-danger { background-color: var(--danger-color); color: white; border-color: var(--danger-color); }
        .btn-subtle-danger { background-color: transparent; color: var(--danger-color); border-color: transparent; }

        /* --- 标签页 --- */
        .tabs { display: flex; gap: 4px; border-bottom: 1px solid var(--border-color); margin-bottom: 24px; flex-shrink: 0; }
        .tab-button { padding: 10px 20px; cursor: pointer; background: none; border: none; border-bottom: 2px solid transparent; font-size: 15px; font-weight: 500; color: var(--text-color-secondary); }
        .tab-button.active { color: var(--primary-color); border-bottom-color: var(--primary-color); }
        .tab-content { display: none; flex-grow: 1; overflow: hidden; height: 100%; }
        .tab-content.active { display: flex; flex-direction: column; }

        /* --- 双栏布局 --- */
        .config-layout { display: grid; grid-template-columns: 350px 1fr; gap: 24px; height: 100%; flex-grow: 1; overflow: hidden; }
        .config-column { background-color: var(--card-bg-color); border-radius: 12px; border: 1px solid var(--border-color); display: flex; flex-direction: column; overflow: hidden; }
        .config-column-header { padding: 16px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center; flex-shrink: 0; }
        .config-column-title { font-size: 16px; font-weight: 600; }
        .config-column-body { overflow-y: auto; flex-grow: 1; }
        .config-column-body.padded { padding: 24px; }
        .config-column-body.list-body { padding: 8px; }

        /* --- 树状视图 --- */
        .tree-view { list-style: none; }
        .tree-item > .tree-item-header { padding: 10px 16px; cursor: pointer; display: flex; align-items: center; gap: 8px; border-radius: 6px; position: relative; }
        .tree-item[data-type="channel"] > .tree-item-header { font-weight: 600; }
        .tree-item[data-type="scene"] { padding-left: 20px; }
        .tree-item .arrow { transition: transform 0.2s; }
        .tree-item.collapsed .arrow { transform: rotate(-90deg); }
        .tree-item.collapsed > .tree-view { display: none; }
        .tree-item[data-type="scene"] > .tree-item-header:hover { background-color: var(--icon-hover-bg); }
        .tree-item[data-type="scene"].active > .tree-item-header { background-color: var(--primary-color-light); }
        .tree-item-actions { display: none; position: absolute; right: 8px; top: 50%; transform: translateY(-50%); background-color: inherit; }
        .tree-item-header:hover .tree-item-actions { display: flex; gap: 4px; }
        .action-btn { cursor: pointer; padding: 2px 6px; border-radius: 4px; font-size: 12px; border: 1px solid var(--border-color); background-color: var(--card-bg-color); color: var(--text-color); }
        .action-btn:hover { background-color: var(--icon-hover-bg); }
        .action-btn.danger:hover { background-color: var(--danger-color); color: white; }
        .action-icon { cursor: pointer; padding: 4px; border-radius: 4px; }
        .action-icon:hover { background-color: var(--border-color); }

        /* --- 标签样式 --- */
        .item-label { font-size: 11px; padding: 2px 6px; border-radius: 4px; font-weight: 500; margin-right: 6px; }
        .label-channel { background-color: rgba(45, 120, 244, 0.1); color: var(--primary-color); }
        .label-scene { background-color: rgba(76, 217, 100, 0.1); color: #4cd964; }

        /* --- 配置策略列表 --- */
        .list-item { padding: 12px 16px; border-radius: 8px; cursor: pointer; transition: background-color 0.2s; margin-bottom: 4px; border: 1px solid transparent; display: flex; justify-content: space-between; align-items: center; }
        .list-item:hover { background-color: var(--icon-hover-bg); }
        .list-item.active { background-color: var(--primary-color-light); border-color: var(--primary-color); font-weight: 500; }
        .list-item-main .name { font-weight: 500; }
        .list-item-main .desc { font-size: 12px; color: var(--text-color-secondary); }
        .badge { padding: 3px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .badge-system { background-color: rgba(100, 116, 139, 0.1); color: var(--text-color-secondary); }

        /* --- 编辑器与表单 --- */
        details { border: 1px solid var(--border-color); border-radius: 8px; margin-bottom: 16px; background-color: var(--bg-color); }
        details[open] { border-color: var(--primary-color); background-color: var(--card-bg-color); }
        summary { padding: 12px 16px; cursor: pointer; font-weight: 600; list-style: none; user-select: none; }
        summary::-webkit-details-marker { display: none; }
        .details-content { padding: 20px; border-top: 1px solid var(--border-color); }
        .details-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 16px 24px; }
        .form-group { margin-bottom: 16px; }
        .form-label { display: block; font-size: 14px; font-weight: 500; color: var(--text-color); margin-bottom: 8px; }
        .form-input, .form-select { width: 100%; padding: 10px 12px; border: 1px solid var(--border-color); border-radius: 6px; background-color: var(--card-bg-color); color: var(--text-color); font-size: 14px; }
        .form-help { font-size: 12px; color: var(--text-color-secondary); margin-top: 6px; }
        .radio-group { display: flex; flex-wrap: wrap; gap: 16px; }
        .radio-group label { display: flex; align-items: center; gap: 6px; font-weight: normal; }
        .checkbox-group { display: grid; grid-template-columns: 1fr 1fr; gap: 8px; }
        .checkbox-group label { display: flex; align-items: center; gap: 8px; font-weight: normal; }
        .toggle-switch { display: flex; justify-content: space-between; align-items: center; }
        .switch { position: relative; display: inline-block; width: 40px; height: 22px; }
        .switch input { opacity: 0; width: 0; height: 0; }
        .slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: var(--border-color); transition: .4s; border-radius: 22px; }
        .slider:before { position: absolute; content: ""; height: 16px; width: 16px; left: 3px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; }
        input:checked + .slider { background-color: var(--primary-color); }
        input:checked + .slider:before { transform: translateX(18px); }

        /* --- 弹窗 Modal --- */
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.5); align-items: center; justify-content: center; }
        .modal.active { display: flex; }
        .modal-content { background-color: var(--card-bg-color); margin: auto; padding: 24px; border: 1px solid var(--border-color); width: 90%; max-width: 500px; border-radius: 12px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .modal-title { font-size: 18px; font-weight: 600; }
        .modal-close { cursor: pointer; font-size: 24px; color: var(--text-color-secondary); }
        .modal-footer { margin-top: 24px; display: flex; justify-content: flex-end; gap: 12px; }

        .empty-state { display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; text-align: center; color: var(--text-color-secondary); padding: 20px; }

        /* --- 场景详情样式 --- */
        .scene-details { border: 1px solid var(--border-color); border-radius: 8px; padding: 16px; margin-bottom: 24px; background-color: var(--bg-color); }
        .scene-details-title { font-size: 16px; font-weight: 600; margin-bottom: 12px; color: var(--text-color); }
        .scene-info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 12px; }
        .scene-info-item { display: flex; flex-direction: column; }
        .scene-info-label { font-size: 12px; color: var(--text-color-secondary); margin-bottom: 4px; }
        .scene-info-value { font-size: 14px; color: var(--text-color); font-weight: 500; }
        .strategy-selection { margin-top: 16px; }
        .strategy-selection-title { font-size: 16px; font-weight: 600; margin-bottom: 12px; color: var(--text-color); }

        /* --- 动作拖拽编辑器样式 --- */
        .dnd-container { display: flex; gap: 16px; }
        .dnd-list { width: 50%; padding: 8px; border-radius: 6px; border: 1px solid var(--border-color); background-color: var(--bg-color); min-height: 150px; transition: background-color 0.2s; }
        .dnd-list h4 { font-size: 13px; font-weight: 500; margin-bottom: 8px; padding: 0 8px 8px; border-bottom: 1px solid var(--border-color); }
        .dnd-list.drag-over { border-color: var(--primary-color); background-color: var(--primary-color-light); }
        .dnd-item { background-color: var(--card-bg-color); padding: 8px 12px; border-radius: 4px; border: 1px solid var(--border-color); margin: 4px; cursor: grab; user-select: none; }
        .dnd-item.dragging { opacity: 0.5; }
        .dnd-item:active { cursor: grabbing; background-color: var(--primary-color-light); }
    </style>
</head>
<body data-theme="light">

    <aside class="sidebar">
        <div class="sidebar-header"><h1 class="sidebar-logo">SSID Platform</h1></div>
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item"><a href="dashboard.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path></svg><span>首页</span></a></li>
                <li class="nav-item active"><a href="configuration.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg><span>配置管理</span></a></li>
                <li class="nav-item"><a href="analytics.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg><span>数据统计</span></a></li>
                <li class="nav-item"><a href="risk-management.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg><span>风险管控</span></a></li>
                <li class="nav-item"><a href="case-audit.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg><span>案例审计</span></a></li>
                <li class="nav-item has-submenu" onclick="toggleSubmenu(this)">
                    <a href="javascript:void(0)">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path></svg>
                        <span>特征库管理</span>
                    </a>
                    <ul class="sub-nav-list">
                        <li class="nav-item"><a href="face-feature-library.html">人脸特征库</a></li>
                        <li class="nav-item"><a href="background-fingerprint-library.html">背景特征库</a></li>
                    </ul>
                </li>
                <li class="nav-item"><a href="distribution.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path></svg><span>资源分发</span></a></li>
                <li class="nav-item"><a href="settings.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 16v-2m8-8h2M4 12H2m15.364 6.364l-1.414-1.414M6.05 6.05L4.636 4.636m12.728 12.728L15.95 15.95M6.05 17.95l1.414-1.414M12 18a6 6 0 100-12 6 6 0 000 12z"></path></svg><span>系统管理</span></a></li>
            </ul>
        </nav>
    </aside>

    <div class="main-wrapper">
        <main class="main-content">
            <header class="page-header">
                <h1 class="page-title">配置管理中心</h1>
            </header>

            <div class="tabs">
                <button class="tab-button active" onclick="openTab(event, 'assignment-tab')">渠道场景管理</button>
                <button class="tab-button" onclick="openTab(event, 'library-tab')">配置策略管理</button>
            </div>

            <!-- Tab 1: 渠道场景管理 -->
            <div id="assignment-tab" class="tab-content active">
                <div class="config-layout">
                    <div class="config-column">
                        <div class="config-column-header">
                            <h2 class="config-column-title">渠道场景管理</h2>
                            <button class="btn btn-secondary btn-sm" onclick="openChannelModal()">+ 增加渠道</button>
                        </div>
                        <div class="config-column-body list-body" id="scene-tree-container"></div>
                    </div>
                    <div class="config-column">
                        <div class="config-column-header">
                            <h2 class="config-column-title" id="assignment-editor-title">场景详情</h2>
                            <button class="btn btn-primary btn-sm" onclick="saveSceneConfig()">保存配置</button>
                        </div>
                        <div class="config-column-body padded" id="assignment-editor-container"></div>
                    </div>
                </div>
            </div>

            <!-- Tab 2: 配置策略库编辑器 -->
            <div id="library-tab" class="tab-content">
                 <div class="config-layout">
                    <div class="config-column">
                        <div class="config-column-header">
                            <h2 class="config-column-title">配置策略库</h2>
                            <button class="btn btn-secondary btn-sm" onclick="openNewPolicyModal()">+ 新建配置策略</button>
                        </div>
                        <div class="config-column-body list-body" id="policy-list-container"></div>
                    </div>
                    <div class="config-column">
                        <div class="config-column-header">
                            <h2 class="config-column-title" id="policy-editor-title">配置策略编辑器</h2>
                            <button class="btn btn-primary btn-sm" onclick="alert('配置策略已保存（模拟）')">保存配置策略</button>
                        </div>
                        <div class="config-column-body padded" id="policy-editor-container"></div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="channel-modal" class="modal"><div class="modal-content"><div class="modal-header"><h3 class="modal-title">增加渠道</h3><span class="modal-close" onclick="closeAllModals()">&times;</span></div><div class="form-group"><label class="form-label">渠道名称</label><input type="text" id="channel-name-input" class="form-input" placeholder="请输入渠道名称"></div><div class="modal-footer"><button class="btn btn-secondary" onclick="closeAllModals()">取消</button><button class="btn btn-primary">保存</button></div></div></div>
    <div id="scene-modal" class="modal"><div class="modal-content"><div class="modal-header"><h3 class="modal-title" id="scene-modal-title">管理场景</h3><span class="modal-close" onclick="closeAllModals()">&times;</span></div><div class="form-group"><label class="form-label">场景名称</label><input type="text" id="scene-name-input" class="form-input"></div><div class="modal-footer"><button class="btn btn-secondary" onclick="closeAllModals()">取消</button><button class="btn btn-primary">保存</button></div></div></div>
    <div id="new-policy-modal" class="modal"><div class="modal-content"><div class="modal-header"><h3 class="modal-title">新建配置策略</h3><span class="modal-close" onclick="closeAllModals()">&times;</span></div><div class="form-group"><label class="form-label">新配置策略名称</label><input type="text" class="form-input" placeholder="例如：电商秒杀专用配置策略"></div><div class="form-group"><label class="form-label">基于模板创建</label><select class="form-select"><option value="p_silent_default">静默模式模板</option><option value="p_interactive_default">交互模式模板</option><option value="p_color_default">炫彩模式模板</option></select></div><div class="modal-footer"><button class="btn btn-secondary" onclick="closeAllModals()">取消</button><button class="btn btn-primary" onclick="alert('新配置策略已创建（模拟）'); closeAllModals();">创建</button></div></div></div>

    <script>
        // --- MOCK DATA & CONFIGS ---
        let mockPolicies = {};
        let mockChannels = { "ch01": { id: "ch01", name: "手机银行 App", scenes: ["sc01", "sc02"] }, "ch02": { id: "ch02", name: "小程序", scenes: ["sc01", "sc03"] } };
        let mockScenes = { "sc01": { id: "sc01", name: "用户注册" }, "sc02": { id: "sc02", name: "大额转账" }, "sc03": { id: "sc03", name: "修改密码" } };
        // 确保每个场景都有配置策略配置，因为创建场景时必须选择配置策略
        let mockAssignments = {
            "ch01_sc01": "p_silent_default",
            "ch01_sc02": "p_interactive_default",
            "ch02_sc01": "p_custom_1",
            "ch02_sc03": "p_color_default"
        };
        const motionMap = { 1: '眨眼', 2: '张嘴', 3: '点头', 4: '摇头' };
        let currentlyDraggedItem = null;

        // --- CORE UI LOGIC ---

        function openTab(evt, tabName) {
            document.querySelectorAll('.tab-content').forEach(tc => tc.classList.remove('active'));
            document.querySelectorAll('.tab-button').forEach(tb => tb.classList.remove('active'));
            document.getElementById(tabName).classList.add('active');
            evt.currentTarget.classList.add('active');
        }

        function renderSceneTree() {
            const container = document.getElementById('scene-tree-container');
            container.innerHTML = `<ul class="tree-view">${Object.values(mockChannels).map(channel => `
                <li class="tree-item" data-type="channel">
                    <div class="tree-item-header" onclick="this.parentElement.classList.toggle('collapsed')">
                       <svg class="arrow" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="6 9 12 15 18 9"></polyline></svg>
                       <span class="item-label label-channel">渠道</span>
                       <span>${channel.name}</span>
                       <div class="tree-item-actions">
                           <button class="action-btn" onclick="event.stopPropagation(); editChannel('${channel.id}')">编辑</button>
                           <button class="action-btn danger" onclick="event.stopPropagation(); deleteChannel('${channel.id}')">删除</button>
                           <span class="action-icon" title="添加场景" onclick="event.stopPropagation(); openSceneModal(null, '${channel.id}')">➕</span>
                       </div>
                    </div>
                    <ul class="tree-view">${channel.scenes.map(scId => {
                        const scene = mockScenes[scId];
                        return `<li class="tree-item" data-type="scene" id="scene-${channel.id}-${scId}" onclick="selectScene('${channel.id}', '${scId}')">
                            <div class="tree-item-header">
                                <span class="item-label label-scene">场景</span>
                                <span>${scene.name}</span>
                                <div class="tree-item-actions">
                                  <button class="action-btn" onclick="event.stopPropagation(); openSceneModal('${scId}', '${channel.id}')">编辑</button>
                                  <button class="action-btn danger" onclick="event.stopPropagation(); deleteScene('${scId}', '${channel.id}')">删除</button>
                                </div>
                            </div></li>`;
                    }).join('')}</ul></li>`).join('')}</ul>`;
        }

        function selectScene(channelId, sceneId) {
            document.querySelectorAll('#scene-tree-container .tree-item[data-type="scene"]').forEach(el => el.classList.remove('active'));
            document.getElementById(`scene-${channelId}-${sceneId}`).classList.add('active');
            document.getElementById('assignment-editor-title').innerText = `场景详情: ${mockScenes[sceneId].name}`;
            renderSceneDetails(channelId, sceneId);
        }

        function renderSceneDetails(channelId, sceneId) {
            const editor = document.getElementById('assignment-editor-container');
            const channel = mockChannels[channelId];
            const scene = mockScenes[sceneId];
            const assignedPolicyId = mockAssignments[`${channelId}_${sceneId}`] || '';
            const policyOptions = Object.values(mockPolicies).map(p => `<option value="${p.id}" ${p.id === assignedPolicyId ? 'selected' : ''}>${p.name}</option>`).join('');

            // 模拟创建时间
            const createTime = new Date().toLocaleString('zh-CN');

            editor.innerHTML = `
                <div class="scene-details">
                    <div class="scene-details-title">场景信息</div>
                    <div class="scene-info-grid">
                        <div class="scene-info-item">
                            <div class="scene-info-label">渠道名称</div>
                            <div class="scene-info-value">${channel.name}</div>
                        </div>
                        <div class="scene-info-item">
                            <div class="scene-info-label">场景名称</div>
                            <div class="scene-info-value">${scene.name}</div>
                        </div>
                        <div class="scene-info-item">
                            <div class="scene-info-label">创建时间</div>
                            <div class="scene-info-value">${createTime}</div>
                        </div>
                        <div class="scene-info-item">
                            <div class="scene-info-label">状态</div>
                            <div class="scene-info-value">${assignedPolicyId ? '已配置配置策略' : '未配置配置策略'}</div>
                        </div>
                    </div>
                </div>

                <div class="strategy-selection">
                    <div class="strategy-selection-title">配置策略选择</div>
                    <div class="form-group">
                        <label class="form-label">为此场景选择一个配置策略 <span style="color: var(--danger-color);">*</span></label>
                        <select class="form-select" id="policy-select-${channelId}-${sceneId}" onchange="updateScenePolicy('${channelId}', '${sceneId}', this.value)" required>
                            <option value="">请选择配置策略</option>
                            ${policyOptions}
                        </select>
                        <div class="form-help">配置策略定义了活体检测的具体模式和所有技术参数。每个场景必须选择一个配置策略才能保存。</div>
                    </div>
                </div>
            `;
        }

        function renderPolicyList() {
            const container = document.getElementById('policy-list-container');
            container.innerHTML = Object.values(mockPolicies).map(p => `
                <div class="list-item" id="policy-item-${p.id}" onclick="selectPolicy('${p.id}')">
                    <div class="list-item-main"><div class="name">${p.name}</div><div class="desc">${p.isDefault ? '系统内置模板' : '自定义配置策略'}</div></div>
                    ${p.isDefault ? '<span class="badge badge-system">默认</span>' : `<button class="btn-subtle-danger btn-sm" onclick="event.stopPropagation(); alert('删除配置策略（模拟）')">删除</button>`}
                </div>`).join('');
        }

        function selectPolicy(policyId) {
             document.querySelectorAll('#policy-list-container .list-item').forEach(el => el.classList.remove('active'));
             document.getElementById(`policy-item-${policyId}`).classList.add('active');
             document.getElementById('policy-editor-title').innerText = `编辑器: ${mockPolicies[policyId].name}`;
             renderPolicyEditor(mockPolicies[policyId]);
        }

        function renderPolicyEditor(policy) {
            const container = document.getElementById('policy-editor-container');
            if (!policy) { container.innerHTML = `<div class="empty-state">请从左侧选择一个配置策略进行编辑。</div>`; return; }

            const createToggle = (path, label) => `<div class="toggle-switch"><label class="form-label">${label}</label><label class="switch"><input type="checkbox" ${getObjectValueByPath(policy.config, path) ? 'checked' : ''}><span class="slider"></span></label></div>`;
            const createNumberInput = (path, label, step = 1, help = '') => `<div class="form-group"><label class="form-label">${label}</label><input type="number" class="form-input" value="${getObjectValueByPath(policy.config, path)}" step="${step}">${help ? `<div class="form-help">${help}</div>` : ''}</div>`;

            const commonParamsHtml = `
                <details><summary>基础与追踪参数</summary><div class="details-content">
                    <details open><summary>基础配置</summary><div class="details-content details-grid">
                        ${createNumberInput('retry_times', '重试次数')} ${createToggle('enable_multi_faces', '允许多人脸检测')}
                    </div></details>
                    <details><summary>追踪参数 (track_config)</summary><div class="details-content details-grid">
                        ${createNumberInput('track_config.detection_threshold', '检测阈值', 0.1)} ${createNumberInput('track_config.iou_threshold', 'IOU阈值', 0.1)}
                    </div></details>
                </div></details>
                <details><summary>质量检测参数 (quality_config)</summary><div class="details-content">
                    <div class="details-grid">${createToggle('quality_config.enable_headpose', '启用姿态检测')} ${createToggle('quality_config.enable_occlusion', '启用遮挡检测')}</div>
                </div></details>`;

            let modeSpecificHtml = '';
            const motionConfig = policy.config.motion_config || {type: 'fixed'};

            switch(policy.mode) {
                case 'silent':
                    modeSpecificHtml = `<details open><summary>静默活体参数 (silent_config)</summary><div class="details-content details-grid">${createNumberInput('silent_config.select_num', '检测帧数')} ${createNumberInput('silent_config.timeout', '超时时间(ms)')}</div></details>`;
                    break;
                case 'interactive':
                case 'color':
                    const motionTypes = { 'fixed': '固定序列', 'random': '随机序列', 'random-two-pools': '随机双动作' };
                    const typeRadios = Object.entries(motionTypes).map(([key, value]) => `<label><input type="radio" name="motion-type-${policy.id}" value="${key}" ${motionConfig.type === key ? 'checked' : ''} onchange="handleMotionTypeChange(event, '${policy.id}')"> ${value}</label>`).join('');

                    modeSpecificHtml = `
                    <details open><summary>动作序列配置</summary><div class="details-content">
                        <div class="form-group"><label class="form-label">动作序列模式</label><div class="radio-group">${typeRadios}</div></div>
                        <div id="motion-config-dynamic-ui-${policy.id}">${renderMotionConfigUI(policy)}</div>
                    </div></details>
                    <details><summary>动作参数</summary><div class="details-content">${createNumberInput('ready_config.timeout', '准备阶段超时(ms)')} ${createNumberInput('blink_config.timeout', '眨眼超时(ms)')}</div></details>`;
                     if(policy.mode === 'color') {
                        modeSpecificHtml += `<details><summary>炫彩活体参数 (color_config)</summary><div class="details-content">${createNumberInput('color_config.color_duration', '颜色停留时间(ms)')}</div></details>`;
                    }
                    break;
            }

            container.innerHTML = `
                <div class="form-group"><label class="form-label">配置策略名称</label><input type="text" class="form-input" value="${policy.name}" ${policy.isDefault ? 'disabled' : ''}></div>
                <div class="form-group"><label class="form-label">活体检测模式</label><select class="form-select" onchange="changePolicyMode(event, '${policy.id}')" ${policy.isDefault ? 'disabled' : ''}>
                    <option value="silent" ${policy.mode === 'silent' ? 'selected' : ''}>静默</option>
                    <option value="interactive" ${policy.mode === 'interactive' ? 'selected' : ''}>交互</option>
                    <option value="color" ${policy.mode === 'color' ? 'selected' : ''}>炫彩</option>
                </select></div>
                <div id="policy-params-container">${commonParamsHtml}${modeSpecificHtml}</div>`;

            if ((policy.mode === 'interactive' || policy.mode === 'color') && motionConfig.type === 'fixed') {
                 attachDragAndDropListeners(policy.id);
            }
        }

        function changePolicyMode(event, policyId) {
            const policy = mockPolicies[policyId];
            if (policy && !policy.isDefault) {
                policy.mode = event.target.value;
                selectPolicy(policyId);
            }
        }

        function renderMotionConfigUI(policy) {
            const motionConfig = policy.config.motion_config || {type: 'fixed'};
            switch(motionConfig.type) {
                case 'fixed':
                    const motions = motionConfig.sequence || [];
                    const availableMotions = Object.keys(motionMap).filter(id => !motions.includes(parseInt(id)));
                    return `<div class="dnd-container" data-policy-id="${policy.id}">
                            <div class="dnd-list" id="dnd-source-${policy.id}"><h4>可选动作</h4>${availableMotions.map(id => `<div class="dnd-item" draggable="true" data-motion-id="${id}">${motionMap[id]}</div>`).join('')}</div>
                            <div class="dnd-list" id="dnd-target-${policy.id}"><h4>已选序列</h4>${motions.map(id => `<div class="dnd-item" draggable="true" data-motion-id="${id}">${motionMap[id]}</div>`).join('')}</div>
                        </div>`;
                case 'random':
                    const pool = motionConfig.pool || [];
                    const count = motionConfig.count || 1;
                    const poolCheckboxes = Object.entries(motionMap).map(([id, name]) => `<label><input type="checkbox" value="${id}" ${pool.includes(parseInt(id)) ? 'checked' : ''}> ${name}</label>`).join('');
                    const maxCount = Math.max(1, pool.length);
                    const countOptions = Array.from({length: maxCount}, (_, i) => i + 1).map(i => `<option value="${i+1}" ${i+1 === count ? 'selected' : ''}>${i+1}</option>`).join('');
                    return `<div class="form-group"><label class="form-label">选择动作池</label><div class="checkbox-group">${poolCheckboxes}</div></div>
                            <div class="form-group"><label class="form-label">随机抽取数量</label><select class="form-select">${countOptions}</select></div>`;
                case 'random-two-pools':
                    const pool1 = motionConfig.pool1 || [];
                    const pool2 = motionConfig.pool2 || [];
                    const createPoolUI = (pool, poolName) => Object.entries(motionMap).map(([id, name]) => {
                        const otherPool = poolName === 'pool1' ? pool2 : pool1;
                        const isDisabled = otherPool.includes(parseInt(id));
                        return `<label><input type="checkbox" value="${id}" ${pool.includes(parseInt(id)) ? 'checked' : ''} ${isDisabled ? 'disabled' : ''}> ${name}</label>`;
                    }).join('');
                    return `<div class="details-grid">
                                <div class="form-group"><label class="form-label">动作池 A (第一步)</label><div class="checkbox-group">${createPoolUI(pool1, 'pool1')}</div></div>
                                <div class="form-group"><label class="form-label">动作池 B (第二步)</label><div class="checkbox-group">${createPoolUI(pool2, 'pool2')}</div></div>
                           </div>`;
                default: return '';
            }
        }

        function handleMotionTypeChange(event, policyId) {
            const policy = mockPolicies[policyId];
            const newType = event.target.value;
            policy.config.motion_config.type = newType;
            if (newType === 'fixed' && !policy.config.motion_config.sequence) policy.config.motion_config.sequence = [];
            if (newType === 'random' && !policy.config.motion_config.pool) { policy.config.motion_config.pool = []; policy.config.motion_config.count = 1; }
            if (newType === 'random-two-pools' && !policy.config.motion_config.pool1) { policy.config.motion_config.pool1 = []; policy.config.motion_config.pool2 = []; }

            const dynamicContainer = document.getElementById(`motion-config-dynamic-ui-${policyId}`);
            dynamicContainer.innerHTML = renderMotionConfigUI(policy);
            if (newType === 'fixed') attachDragAndDropListeners(policyId);
        }

        function attachDragAndDropListeners(policyId) {
            const container = document.querySelector(`.dnd-container[data-policy-id="${policyId}"]`);
            if (!container) return;
            container.addEventListener('dragstart', e => { if (e.target.classList.contains('dnd-item')) { currentlyDraggedItem = e.target; setTimeout(() => e.target.classList.add('dragging'), 0); } });
            container.addEventListener('dragend', () => { if(currentlyDraggedItem) { currentlyDraggedItem.classList.remove('dragging'); currentlyDraggedItem = null; updatePolicyMotionsFromDOM(policyId); } });
            container.querySelectorAll('.dnd-list').forEach(list => {
                list.addEventListener('dragover', e => {
                    e.preventDefault(); const afterElement = getDragAfterElement(list, e.clientY);
                    const draggable = document.querySelector('.dragging');
                    if (draggable) { if (afterElement == null) { list.appendChild(draggable); } else { list.insertBefore(draggable, afterElement); } }
                });
            });
        }

        function getDragAfterElement(container, y) {
            const draggableElements = [...container.querySelectorAll('.dnd-item:not(.dragging)')];
            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect(); const offset = y - box.top - box.height / 2;
                return (offset < 0 && offset > closest.offset) ? { offset: offset, element: child } : closest;
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        }

        function updatePolicyMotionsFromDOM(policyId) {
            const targetList = document.getElementById(`dnd-target-${policyId}`);
            if (targetList) {
                mockPolicies[policyId].config.motion_config.sequence = Array.from(targetList.querySelectorAll('.dnd-item')).map(item => parseInt(item.dataset.motionId));
            }
        }

        const getObjectValueByPath = (obj, path) => path.split('.').reduce((acc, part) => acc && acc[part] !== undefined ? acc[part] : null, obj);

        // 新增的功能函数
        function editChannel(channelId) {
            alert(`编辑渠道: ${mockChannels[channelId].name}（模拟功能）`);
        }

        function deleteChannel(channelId) {
            if (confirm(`确定要删除渠道"${mockChannels[channelId].name}"吗？这将同时删除该渠道下的所有场景。`)) {
                alert('删除渠道（模拟功能）');
            }
        }

        function deleteScene(sceneId, channelId) {
            if (confirm(`确定要删除场景"${mockScenes[sceneId].name}"吗？`)) {
                alert('删除场景（模拟功能）');
            }
        }

        function updateScenePolicy(channelId, sceneId, policyId) {
            if (policyId) {
                mockAssignments[`${channelId}_${sceneId}`] = policyId;
                // 更新场景详情显示
                renderSceneDetails(channelId, sceneId);
            } else {
                delete mockAssignments[`${channelId}_${sceneId}`];
            }
        }

        function saveSceneConfig() {
            // 检查当前选中的场景是否已配置配置策略
            const activeScene = document.querySelector('#scene-tree-container .tree-item[data-type="scene"].active');
            if (!activeScene) {
                alert('请先选择一个场景');
                return;
            }

            const sceneId = activeScene.id.split('-')[2];
            const channelId = activeScene.id.split('-')[1];
            const assignedPolicy = mockAssignments[`${channelId}_${sceneId}`];

            if (!assignedPolicy) {
                alert('请为场景选择一个配置策略后再保存');
                return;
            }

            alert('场景配置已保存（模拟）');
        }
        function openChannelModal() { document.getElementById('channel-modal').classList.add('active'); }
        function openSceneModal(sceneId, channelId) {
            document.getElementById('scene-modal-title').innerText = sceneId ? `编辑场景` : `为“${mockChannels[channelId].name}”添加新场景`;
            document.getElementById('scene-name-input').value = sceneId ? mockScenes[sceneId].name : '';
            document.getElementById('scene-modal').classList.add('active');
        }
        function openNewPolicyModal() { document.getElementById('new-policy-modal').classList.add('active'); }
        function closeAllModals() { document.querySelectorAll('.modal').forEach(m => m.classList.remove('active')); }

        // --- INITIALIZATION ---
        document.addEventListener('DOMContentLoaded', () => {
            const silentConfig = {"config":{"enable_multi_faces":true,"retry_times":3,"track_config":{"detection_threshold":0.6,"iou_threshold":0.5},"quality_config":{"enable_headpose":true,"enable_occlusion":true},"silent_config":{"select_num":10,"timeout":10000}}};
            const interactiveConfig = {"config":{"retry_times":3,"track_config":{"detection_threshold":0.6},"quality_config":{"enable_headpose":true},"ready_config":{"timeout":10000},"blink_config":{"timeout":10000},"motion_config":{"type":"fixed", "sequence":[1,2]}}};
            const colorConfig = {"config":{"retry_times":1,"track_config":{"detection_threshold":0.6},"quality_config":{"enable_headpose":true},"color_config":{"color_duration":500,"timeout":10000},"motion_config":{"type":"random-two-pools", "pool1":[1,2], "pool2":[3,4]}}};

            mockPolicies = {
                'p_silent_default': { id: 'p_silent_default', name: '静默模式模板', isDefault: true, mode: 'silent', ...JSON.parse(JSON.stringify(silentConfig)) },
                'p_interactive_default': { id: 'p_interactive_default', name: '交互模式模板', isDefault: true, mode: 'interactive', ...JSON.parse(JSON.stringify(interactiveConfig)) },
                'p_color_default': { id: 'p_color_default', name: '炫彩模式模板', isDefault: true, mode: 'color', ...JSON.parse(JSON.stringify(colorConfig)) },
                'p_custom_1': { id: 'p_custom_1', name: '电商秒杀专用配置策略', isDefault: false, mode: 'interactive', ...JSON.parse(JSON.stringify(interactiveConfig)) },
            };

            // Initial render
            renderSceneTree();
            document.getElementById('assignment-editor-container').innerHTML = `<div class="empty-state">请从左侧选择一个场景进行配置策略配置</div>`;
            renderPolicyList();
            document.getElementById('policy-editor-container').innerHTML = `<div class="empty-state">请从左侧选择一个配置策略进行编辑</div>`;
        }

        // 子菜单展开/收起功能
        function toggleSubmenu(element) {
            element.classList.toggle('expanded');
        });
    </script>
</body>
</html>