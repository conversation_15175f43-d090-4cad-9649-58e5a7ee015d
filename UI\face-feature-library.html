<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSID Platform - 人脸特征库</title>
    <!-- [此处省略了与之前版本相同的CSS样式代码] -->
    <style>
        :root { --bg-color: #f6f8fb; --sidebar-bg-color: #ffffff; --card-bg-color: #ffffff; --text-color: #1e293b; --text-color-secondary: #64748b; --border-color: #e5eaf3; --primary-color: #2d78f4; --primary-color-light: #f0f6ff; --shadow-color: rgba(0,0,0,0.06); --icon-color: #9ca3af; --icon-hover-bg: #f1f5f9; }
        [data-theme="dark"] { --bg-color: #111827; --sidebar-bg-color: #1f2937; --card-bg-color: #1f2937; --text-color: #f9fafb; --text-color-secondary: #9ca3af; --border-color: #374151; --primary-color: #3b82f6; --primary-color-light: #252e3d; --shadow-color: rgba(0,0,0,0.2); --icon-color: #9ca3af; --icon-hover-bg: #374151; }
        * { margin: 0; padding: 0; box-sizing: border-box; font-family: "PingFang SC", "Microsoft YaHei", sans-serif; }
        html, body { height: 100%; overflow: hidden; }
        body { background-color: var(--bg-color); color: var(--text-color); transition: background-color 0.3s, color 0.3s; display: flex; }
        .sidebar { width: 260px; background-color: var(--sidebar-bg-color); border-right: 1px solid var(--border-color); display: flex; flex-direction: column; height: 100%; flex-shrink: 0; transition: background-color 0.3s, border-color 0.3s; }
        .sidebar-header { display: flex; align-items: center; padding: 0 24px; height: 64px; border-bottom: 1px solid var(--border-color); flex-shrink: 0; }
        .sidebar-logo { font-size: 22px; font-weight: 700; color: var(--primary-color); }
        .sidebar-nav { flex-grow: 1; padding: 16px 0; }
        .nav-list { list-style: none; }
        .nav-item a { display: flex; align-items: center; gap: 12px; padding: 12px 24px; margin: 4px 16px; border-radius: 8px; color: var(--text-color-secondary); text-decoration: none; font-weight: 500; transition: background-color 0.2s, color 0.2s; }
        .nav-item a:hover { background-color: var(--primary-color-light); color: var(--primary-color); }
        .nav-item.active a { background-color: var(--primary-color-light); color: var(--primary-color); font-weight: 600; }
        .nav-icon { width: 20px; height: 20px; }
        .sub-nav-list { list-style: none; margin-left: 16px; }
        .sub-nav-list .nav-item a { padding: 8px 24px; margin: 2px 16px; font-size: 13px; }
        .sub-nav-list .nav-item a:before { content: "•"; margin-right: 8px; color: var(--text-color-secondary); }
        .main-wrapper { flex-grow: 1; height: 100%; overflow-y: auto; }
        .main-content { padding: 24px; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .page-title { font-size: 24px; font-weight: 700; }
        .btn { padding: 8px 16px; border-radius: 6px; border: none; font-size: 14px; cursor: pointer; font-weight: 500; }
        .btn-primary { background-color: var(--primary-color); color: white; }
        .btn-secondary { background-color: var(--card-bg-color); color: var(--text-color); border: 1px solid var(--border-color); }
        .btn-danger { background-color: #ff3b30; color: white; }
        .card { background-color: var(--card-bg-color); border-radius: 12px; margin-bottom: 24px; border: 1px solid var(--border-color); }
        .card-header { padding: 16px 24px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center; }
        .card-title { font-size: 17px; font-weight: 600; }
        .card-content { padding: 0; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { text-align: left; padding: 12px 16px; border-bottom: 1px solid var(--border-color); vertical-align: middle; }
        .table th { color: var(--text-color-secondary); font-weight: 500; }
        .action-buttons { display: flex; gap: 8px; }
        .user-photo { width: 40px; height: 40px; border-radius: 50%; object-fit: cover; }
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); align-items: center; justify-content: center; }
        .modal.active { display: flex; }
        .modal-content { background-color: var(--card-bg-color); padding: 24px; width: 90%; max-width: 500px; border-radius: 12px; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .modal-title { font-size: 18px; font-weight: 600; }
        .modal-close { cursor: pointer; font-size: 24px; }
        .modal-footer { margin-top: 24px; display: flex; justify-content: flex-end; gap: 12px; }
        .form-group { margin-bottom: 16px; }
        .form-label { display: block; font-weight: 500; margin-bottom: 8px; }
        .form-input { width: 100%; padding: 10px 12px; border: 1px solid var(--border-color); border-radius: 6px; }
    </style>
</head>
<body>
    <aside class="sidebar">
        <div class="sidebar-header"><h1 class="sidebar-logo">SSID Platform</h1></div>
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="dashboard.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path></svg>
                        <span>首页</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="configuration.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>
                        <span>配置管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="analytics.html">
                       <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>
                        <span>数据统计</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="risk-management.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg>
                        <span>风险管控</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="case-audit.html">
                       <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>
                        <span>案例审计</span>
                    </a>
                </li>
                <li class="nav-item has-submenu active expanded" onclick="toggleSubmenu(this)">
                    <a href="javascript:void(0)">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path></svg>
                        <span>特征库管理</span>
                    </a>
                    <ul class="sub-nav-list">
                        <li class="nav-item active"><a href="face-feature-library.html">人脸特征库</a></li>
                        <li class="nav-item"><a href="background-fingerprint-library.html">背景特征库</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a href="distribution.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path></svg>
                        <span>资源分发</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="settings.html">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 16v-2m8-8h2M4 12H2m15.364 6.364l-1.414-1.414M6.05 6.05L4.636 4.636m12.728 12.728L15.95 15.95M6.05 17.95l1.414-1.414M12 18a6 6 0 100-12 6 6 0 000 12z"></path></svg>
                        <span>系统管理</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <div class="main-wrapper">
        <main class="main-content">
            <!-- 库列表 (主视图) -->
            <div id="library-list-view">
                <header class="page-header">
                    <h1 class="page-title">人脸特征库</h1>
                    <button class="btn btn-primary" onclick="openNewLibraryModal()">+ 新建人脸库</button>
                </header>
                <div class="card">
                    <div class="card-content">
                        <table class="table">
                            <thead><tr><th>特征库名称</th><th>特征数量</th><th>库类型</th><th>备注</th><th>创建时间</th><th>操作</th></tr></thead>
                            <tbody>
                                <tr><td>VIP客户人脸库</td><td>45,210 / 100,000</td><td><span style="color: #34c759; font-weight: 500;">白名单</span></td><td>高净值客户群体</td><td>2023-03-20</td><td class="action-buttons"><button class="btn btn-secondary" onclick="showUserManagementView()">管理成员</button><button class="btn btn-secondary">编辑</button></td></tr>
                                <tr><td>员工人脸库</td><td>8,750 / 10,000</td><td><span style="color: #34c759; font-weight: 500;">白名单</span></td><td>内部员工白名单</td><td>2023-01-15</td><td class="action-buttons"><button class="btn btn-secondary" onclick="showUserManagementView()">管理成员</button><button class="btn btn-secondary">编辑</button></td></tr>
                                <tr><td>已知欺诈人脸库</td><td>1,247</td><td><span style="color: #ff3b30; font-weight: 500;">黑名单</span></td><td>历史欺诈用户</td><td>2023-08-01</td><td class="action-buttons"><button class="btn btn-secondary" onclick="showUserManagementView()">管理成员</button><button class="btn btn-secondary">编辑</button></td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 库成员管理 (详情视图, 默认隐藏) -->
            <div id="user-management-view" style="display: none;">
                 <header class="page-header">
                    <div>
                        <button class="btn btn-secondary" onclick="showLibraryListView()">&larr; 返回库列表</button>
                        <h1 class="page-title" style="display: inline-block; margin-left: 16px;">管理成员: VIP客户人脸库</h1>
                    </div>
                    <div>
                        <input type="search" class="form-input" placeholder="按用户ID搜索..." style="display:inline-block; width: 200px; vertical-align: middle; margin-right: 12px;">
                        <button class="btn btn-secondary" onclick="openBatchImportModal()">批量导入</button>
                        <button class="btn btn-secondary" onclick="openBatchDeleteModal()">批量删除</button>
                        <button class="btn btn-secondary" onclick="exportUserList()">导出列表</button>
                        <button class="btn btn-primary" onclick="openAddUserModal()">+ 添加成员</button>
                    </div>
                </header>
                <div class="card">
                     <div class="card-content">
                        <table class="table">
                            <thead><tr><th><input type="checkbox" id="select-all" onchange="toggleSelectAll()"></th><th>照片</th><th>用户ID</th><th>特征质量</th><th>入库时间</th><th>操作</th></tr></thead>
                            <tbody>
                                <tr><td><input type="checkbox" class="user-checkbox" value="user_vip_001"></td><td><img src="https://i.pravatar.cc/150?img=1" alt="User" class="user-photo"></td><td>user_vip_001</td><td><span style="color: #34c759; font-weight: 500;">优秀 (0.95)</span></td><td>2023-03-21</td><td class="action-buttons"><button class="btn btn-secondary" onclick="openUpdatePhotoModal('user_vip_001')">更新照片</button><button class="btn btn-secondary" onclick="viewFeatureDetails('user_vip_001')">查看特征</button><button class="btn-danger" style="background: none; color: #ff3b30;" onclick="openDeleteModal('user_vip_001')">删除</button></td></tr>
                                <tr><td><input type="checkbox" class="user-checkbox" value="user_vip_002"></td><td><img src="https://i.pravatar.cc/150?img=2" alt="User" class="user-photo"></td><td>user_vip_002</td><td><span style="color: #ff9500; font-weight: 500;">良好 (0.82)</span></td><td>2023-03-22</td><td class="action-buttons"><button class="btn btn-secondary" onclick="openUpdatePhotoModal('user_vip_002')">更新照片</button><button class="btn btn-secondary" onclick="viewFeatureDetails('user_vip_002')">查看特征</button><button class="btn-danger" style="background: none; color: #ff3b30;" onclick="openDeleteModal('user_vip_002')">删除</button></td></tr>
                                <tr><td><input type="checkbox" class="user-checkbox" value="user_vip_003"></td><td><img src="https://i.pravatar.cc/150?img=3" alt="User" class="user-photo"></td><td>user_vip_003</td><td><span style="color: #ff3b30; font-weight: 500;">较差 (0.65)</span></td><td>2023-03-23</td><td class="action-buttons"><button class="btn btn-secondary" onclick="openUpdatePhotoModal('user_vip_003')">更新照片</button><button class="btn btn-secondary" onclick="viewFeatureDetails('user_vip_003')">查看特征</button><button class="btn-danger" style="background: none; color: #ff3b30;" onclick="openDeleteModal('user_vip_003')">删除</button></td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="new-library-modal" class="modal"><div class="modal-content"><div class="modal-header"><h3 class="modal-title">新建人脸库</h3><span class="modal-close" onclick="closeAllModals()">&times;</span></div><div class="form-group"><label class="form-label">特征库名称</label><input type="text" class="form-input"></div><div class="form-group"><label class="form-label">备注</label><input type="text" class="form-input"></div><div class="modal-footer"><button class="btn btn-secondary" onclick="closeAllModals()">取消</button><button class="btn btn-primary">创建</button></div></div></div>
    <div id="add-user-modal" class="modal"><div class="modal-content"><div class="modal-header"><h3 class="modal-title">添加新成员</h3><span class="modal-close" onclick="closeAllModals()">&times;</span></div><div class="form-group"><label class="form-label">用户ID</label><input type="text" class="form-input"></div><div class="form-group"><label class="form-label">上传照片</label><input type="file" class="form-input"></div><div class="modal-footer"><button class="btn btn-secondary" onclick="closeAllModals()">取消</button><button class="btn btn-primary">确认添加</button></div></div></div>
    <div id="delete-modal" class="modal"><div class="modal-content"><div class="modal-header"><h3 class="modal-title">确认删除</h3><span class="modal-close" onclick="closeAllModals()">&times;</span></div><p>您确定要删除这个成员吗？此操作无法撤销。</p><div class="modal-footer"><button class="btn btn-secondary" onclick="closeAllModals()">取消</button><button class="btn btn-danger">确认删除</button></div></div></div>

    <!-- 批量导入模态框 -->
    <div id="batch-import-modal" class="modal">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3 class="modal-title">批量导入成员</h3>
                <span class="modal-close" onclick="closeAllModals()">&times;</span>
            </div>
            <div class="form-group">
                <label class="form-label">导入方式</label>
                <select class="form-select" onchange="toggleImportMethod(this.value)">
                    <option value="file">文件导入</option>
                    <option value="folder">文件夹导入</option>
                    <option value="template">模板导入</option>
                </select>
            </div>
            <div id="file-import" class="form-group">
                <label class="form-label">选择文件</label>
                <input type="file" class="form-input" accept=".csv,.xlsx,.zip" multiple>
                <small style="color: var(--text-color-secondary);">支持CSV、Excel或包含图片的ZIP文件</small>
            </div>
            <div id="folder-import" class="form-group" style="display: none;">
                <label class="form-label">选择文件夹</label>
                <input type="file" class="form-input" webkitdirectory>
                <small style="color: var(--text-color-secondary);">选择包含用户照片的文件夹，文件名作为用户ID</small>
            </div>
            <div class="form-group">
                <label class="form-label">特征质量阈值</label>
                <select class="form-select">
                    <option>0.6 (宽松)</option>
                    <option selected>0.7 (标准)</option>
                    <option>0.8 (严格)</option>
                </select>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeAllModals()">取消</button>
                <button class="btn btn-primary">开始导入</button>
            </div>
        </div>
    </div>

    <!-- 批量删除模态框 -->
    <div id="batch-delete-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">批量删除成员</h3>
                <span class="modal-close" onclick="closeAllModals()">&times;</span>
            </div>
            <p>您确定要删除选中的 <span id="selected-count">0</span> 个成员吗？此操作无法撤销。</p>
            <div class="form-group">
                <label class="form-label">删除原因</label>
                <select class="form-select">
                    <option>数据清理</option>
                    <option>重复数据</option>
                    <option>质量不达标</option>
                    <option>用户要求</option>
                    <option>其他</option>
                </select>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeAllModals()">取消</button>
                <button class="btn btn-danger">确认删除</button>
            </div>
        </div>
    </div>

    <!-- 更新照片模态框 -->
    <div id="update-photo-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">更新用户照片</h3>
                <span class="modal-close" onclick="closeAllModals()">&times;</span>
            </div>
            <div class="form-group">
                <label class="form-label">用户ID</label>
                <input type="text" class="form-input" id="update-user-id" readonly>
            </div>
            <div class="form-group">
                <label class="form-label">当前照片</label>
                <img id="current-photo" src="" alt="Current Photo" class="user-photo" style="width: 80px; height: 80px;">
            </div>
            <div class="form-group">
                <label class="form-label">新照片</label>
                <input type="file" class="form-input" accept="image/*">
            </div>
            <div class="form-group">
                <label class="form-label">更新原因</label>
                <input type="text" class="form-input" placeholder="请输入更新原因">
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeAllModals()">取消</button>
                <button class="btn btn-primary">确认更新</button>
            </div>
        </div>
    </div>

    <script>
        function showView(viewIdToShow) {
            document.getElementById('library-list-view').style.display = 'none';
            document.getElementById('user-management-view').style.display = 'none';
            document.getElementById(viewIdToShow).style.display = 'block';
        }
        function showLibraryListView() { showView('library-list-view'); }
        function showUserManagementView() { showView('user-management-view'); }
        function openNewLibraryModal() { document.getElementById('new-library-modal').classList.add('active'); }
        function openAddUserModal() { document.getElementById('add-user-modal').classList.add('active'); }
        function openDeleteModal(userId) {
            document.getElementById('delete-modal').classList.add('active');
        }
        function openBatchImportModal() { document.getElementById('batch-import-modal').classList.add('active'); }
        function openBatchDeleteModal() {
            const selectedCount = document.querySelectorAll('.user-checkbox:checked').length;
            document.getElementById('selected-count').textContent = selectedCount;
            document.getElementById('batch-delete-modal').classList.add('active');
        }
        function openUpdatePhotoModal(userId) {
            document.getElementById('update-user-id').value = userId;
            document.getElementById('current-photo').src = `https://i.pravatar.cc/150?img=${userId.slice(-1)}`;
            document.getElementById('update-photo-modal').classList.add('active');
        }
        function viewFeatureDetails(userId) {
            alert(`查看用户 ${userId} 的特征详情`);
        }
        function exportUserList() {
            alert('导出用户列表功能');
        }
        function toggleSelectAll() {
            const selectAll = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.user-checkbox');
            checkboxes.forEach(cb => cb.checked = selectAll.checked);
        }
        function toggleImportMethod(method) {
            document.getElementById('file-import').style.display = method === 'file' ? 'block' : 'none';
            document.getElementById('folder-import').style.display = method === 'folder' ? 'block' : 'none';
        }
        function closeAllModals() { document.querySelectorAll('.modal').forEach(m => m.classList.remove('active')); }

        // 子菜单展开/收起功能
        function toggleSubmenu(element) {
            element.classList.toggle('expanded');
        }
    </script>
</body>
</html>