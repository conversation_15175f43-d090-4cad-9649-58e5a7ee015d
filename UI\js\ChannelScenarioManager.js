/**
 * 渠道场景管理器
 * 负责渠道和场景的层级管理、CRUD操作
 */
class ChannelScenarioManager {
    constructor() {
        this.channels = new Map();
        this.scenarios = new Map();
        this.init();
    }

    /**
     * 初始化管理器
     */
    init() {
        this.loadChannelsAndScenarios();
        this.bindEvents();
    }

    /**
     * 加载渠道和场景数据
     */
    async loadChannelsAndScenarios() {
        try {
            // 模拟从API加载数据
            const mockData = {
                channels: [
                    {
                        id: 'mobile-app',
                        name: '手机银行 App',
                        description: '移动端银行应用',
                        code: 'mobile_app',
                        defaultThresholds: {
                            liveness: 0.8,
                            comparison: 0.75,
                            deepfake: 0.9
                        },
                        createdAt: '2023-04-10',
                        status: 'active'
                    },
                    {
                        id: 'mini-program',
                        name: '小程序',
                        description: '微信小程序',
                        code: 'mini_program',
                        defaultThresholds: {
                            liveness: 0.85,
                            comparison: 0.8,
                            deepfake: 0.95
                        },
                        createdAt: '2023-05-21',
                        status: 'active'
                    },
                    {
                        id: 'web',
                        name: '网页端',
                        description: '网页版银行',
                        code: 'web',
                        defaultThresholds: {
                            liveness: 0.75,
                            comparison: 0.7,
                            deepfake: 0.85
                        },
                        createdAt: '2023-06-01',
                        status: 'active'
                    }
                ],
                scenarios: [
                    {
                        id: 'user-register',
                        name: '用户注册',
                        description: '新用户注册验证',
                        code: 'user_register',
                        channelId: 'mobile-app',
                        thresholdMode: 'inherit',
                        createdAt: '2023-04-11',
                        status: 'active'
                    },
                    {
                        id: 'large-transfer',
                        name: '大额转账',
                        description: '大额资金转账验证',
                        code: 'large_transfer',
                        channelId: 'mobile-app',
                        thresholdMode: 'custom',
                        customThresholds: {
                            liveness: 0.9,
                            comparison: 0.85,
                            deepfake: 0.95
                        },
                        createdAt: '2023-06-15',
                        status: 'active'
                    },
                    {
                        id: 'password-reset',
                        name: '密码重置',
                        description: '用户密码重置验证',
                        code: 'password_reset',
                        channelId: 'mobile-app',
                        thresholdMode: 'inherit',
                        createdAt: '2023-07-20',
                        status: 'active'
                    },
                    {
                        id: 'user-register-mini',
                        name: '用户注册',
                        description: '小程序用户注册验证',
                        code: 'user_register_mini',
                        channelId: 'mini-program',
                        thresholdMode: 'inherit',
                        createdAt: '2023-05-22',
                        status: 'active'
                    }
                ]
            };

            // 存储到Map中
            mockData.channels.forEach(channel => {
                this.channels.set(channel.id, channel);
            });

            mockData.scenarios.forEach(scenario => {
                this.scenarios.set(scenario.id, scenario);
            });

            this.renderChannelScenarioTree();
            this.updatePolicyFormSelectors();

        } catch (error) {
            console.error('加载渠道场景数据失败:', error);
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 树形结构展开/收起
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tree-toggle')) {
                this.toggleTreeNode(e.target);
            }
        });
    }

    /**
     * 渲染渠道场景树
     */
    renderChannelScenarioTree() {
        const container = document.querySelector('.channel-scenario-tree');
        if (!container) return;

        container.innerHTML = '';

        // 按渠道分组场景
        const channelScenarios = new Map();
        this.scenarios.forEach(scenario => {
            if (!channelScenarios.has(scenario.channelId)) {
                channelScenarios.set(scenario.channelId, []);
            }
            channelScenarios.get(scenario.channelId).push(scenario);
        });

        // 渲染每个渠道
        this.channels.forEach(channel => {
            const scenarios = channelScenarios.get(channel.id) || [];
            const channelNode = this.createChannelNode(channel, scenarios);
            container.appendChild(channelNode);
        });
    }

    /**
     * 创建渠道节点
     */
    createChannelNode(channel, scenarios) {
        const node = document.createElement('div');
        node.className = 'tree-node channel-node';
        node.dataset.channelId = channel.id;

        const policyCount = this.getChannelPolicyCount(channel.id);
        const hasScenarios = scenarios.length > 0;

        node.innerHTML = `
            <div class="tree-node-header">
                <span class="tree-toggle ${hasScenarios ? '' : 'collapsed'}" onclick="toggleTreeNode(this)">
                    ${hasScenarios ? '▼' : '▶'}
                </span>
                <span class="tree-label">${channel.name}</span>
                <span class="badge badge-blue">渠道</span>
                <div class="tree-actions">
                    <button class="btn btn-secondary btn-sm" onclick="editChannel('${channel.id}')">编辑</button>
                    <button class="btn btn-secondary btn-sm" onclick="addScenarioToChannel('${channel.id}')">添加场景</button>
                    <button class="btn btn-danger btn-sm" onclick="deleteChannel('${channel.id}')">删除</button>
                </div>
            </div>
            <div class="tree-children" ${hasScenarios ? '' : 'style="display: none;"'}>
                ${hasScenarios ? 
                    scenarios.map(scenario => this.createScenarioNodeHTML(scenario)).join('') :
                    `<div class="empty-scenarios">
                        <span class="empty-text">暂无场景</span>
                        <button class="btn btn-primary btn-sm" onclick="addScenarioToChannel('${channel.id}')">添加第一个场景</button>
                    </div>`
                }
            </div>
        `;

        return node;
    }

    /**
     * 创建场景节点HTML
     */
    createScenarioNodeHTML(scenario) {
        const policyCount = this.getScenarioPolicyCount(scenario.id);
        const thresholdModeText = scenario.thresholdMode === 'custom' ? '自定义阈值' : '继承阈值';
        const thresholdModeColor = scenario.thresholdMode === 'custom' ? 'badge-orange' : 'badge-blue';

        return `
            <div class="tree-node scenario-node" data-scenario-id="${scenario.id}">
                <div class="tree-node-content">
                    <span class="tree-label">${scenario.name}</span>
                    <span class="badge badge-green">场景</span>
                    <span class="badge ${thresholdModeColor}">${thresholdModeText}</span>
                    <span class="scenario-info">策略数: ${policyCount}</span>
                    <div class="tree-actions">
                        <button class="btn btn-secondary btn-sm" onclick="editScenario('${scenario.id}')">编辑</button>
                        <button class="btn btn-danger btn-sm" onclick="deleteScenario('${scenario.id}')">删除</button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取风险等级文本
     */


    /**
     * 切换树节点展开/收起
     */
    toggleTreeNode(toggleElement) {
        const treeNode = toggleElement.closest('.tree-node');
        const children = treeNode.querySelector('.tree-children');
        
        if (toggleElement.classList.contains('collapsed')) {
            toggleElement.classList.remove('collapsed');
            toggleElement.textContent = '▼';
            children.style.display = 'block';
        } else {
            toggleElement.classList.add('collapsed');
            toggleElement.textContent = '▶';
            children.style.display = 'none';
        }
    }

    /**
     * 创建渠道
     */
    async createChannel(channelData) {
        try {
            // 验证数据
            if (!this.validateChannelData(channelData)) {
                return false;
            }

            // 检查唯一性
            if (this.isChannelCodeExists(channelData.code)) {
                throw new Error('渠道标识已存在');
            }

            // 生成ID
            const id = this.generateId('channel');
            const channel = {
                id,
                ...channelData,
                createdAt: new Date().toISOString().split('T')[0],
                status: 'active'
            };

            // 保存到本地存储
            this.channels.set(id, channel);
            
            // 模拟API调用
            await this.saveChannelToServer(channel);

            // 刷新界面
            this.renderChannelScenarioTree();
            this.updatePolicyFormSelectors();

            return true;
        } catch (error) {
            console.error('创建渠道失败:', error);
            throw error;
        }
    }

    /**
     * 创建场景
     */
    async createScenario(scenarioData) {
        try {
            // 验证数据
            if (!this.validateScenarioData(scenarioData)) {
                return false;
            }

            // 检查唯一性
            if (this.isScenarioCodeExists(scenarioData.code, scenarioData.channelId)) {
                throw new Error('该渠道下场景标识已存在');
            }

            // 生成ID
            const id = this.generateId('scenario');
            const scenario = {
                id,
                ...scenarioData,
                createdAt: new Date().toISOString().split('T')[0],
                status: 'active'
            };

            // 保存到本地存储
            this.scenarios.set(id, scenario);
            
            // 模拟API调用
            await this.saveScenarioToServer(scenario);

            // 刷新界面
            this.renderChannelScenarioTree();
            this.updatePolicyFormSelectors();

            return true;
        } catch (error) {
            console.error('创建场景失败:', error);
            throw error;
        }
    }

    /**
     * 更新渠道
     */
    async updateChannel(channelId, channelData) {
        try {
            const existingChannel = this.channels.get(channelId);
            if (!existingChannel) {
                throw new Error('渠道不存在');
            }

            // 验证数据
            if (!this.validateChannelData(channelData)) {
                return false;
            }

            // 检查唯一性（排除自身）
            if (channelData.code !== existingChannel.code && this.isChannelCodeExists(channelData.code)) {
                throw new Error('渠道标识已存在');
            }

            const updatedChannel = {
                ...existingChannel,
                ...channelData,
                updatedAt: new Date().toISOString().split('T')[0]
            };

            // 更新本地存储
            this.channels.set(channelId, updatedChannel);
            
            // 模拟API调用
            await this.updateChannelOnServer(updatedChannel);

            // 刷新界面
            this.renderChannelScenarioTree();
            this.updatePolicyFormSelectors();

            return true;
        } catch (error) {
            console.error('更新渠道失败:', error);
            throw error;
        }
    }

    /**
     * 更新场景
     */
    async updateScenario(scenarioId, scenarioData) {
        try {
            const existingScenario = this.scenarios.get(scenarioId);
            if (!existingScenario) {
                throw new Error('场景不存在');
            }

            // 验证数据
            if (!this.validateScenarioData(scenarioData)) {
                return false;
            }

            // 检查唯一性（排除自身）
            if (scenarioData.code !== existingScenario.code && 
                this.isScenarioCodeExists(scenarioData.code, scenarioData.channelId)) {
                throw new Error('该渠道下场景标识已存在');
            }

            const updatedScenario = {
                ...existingScenario,
                ...scenarioData,
                updatedAt: new Date().toISOString().split('T')[0]
            };

            // 更新本地存储
            this.scenarios.set(scenarioId, updatedScenario);
            
            // 模拟API调用
            await this.updateScenarioOnServer(updatedScenario);

            // 刷新界面
            this.renderChannelScenarioTree();
            this.updatePolicyFormSelectors();

            return true;
        } catch (error) {
            console.error('更新场景失败:', error);
            throw error;
        }
    }

    /**
     * 删除渠道
     */
    async deleteChannel(channelId) {
        try {
            const channel = this.channels.get(channelId);
            if (!channel) {
                throw new Error('渠道不存在');
            }

            // 检查是否有关联的场景
            const relatedScenarios = Array.from(this.scenarios.values())
                .filter(scenario => scenario.channelId === channelId);
            
            if (relatedScenarios.length > 0) {
                const confirmed = confirm(`该渠道下有 ${relatedScenarios.length} 个场景，删除渠道将同时删除所有场景，确定继续吗？`);
                if (!confirmed) {
                    return false;
                }
            }

            // 删除关联场景
            relatedScenarios.forEach(scenario => {
                this.scenarios.delete(scenario.id);
            });

            // 删除渠道
            this.channels.delete(channelId);
            
            // 模拟API调用
            await this.deleteChannelOnServer(channelId);

            // 刷新界面
            this.renderChannelScenarioTree();
            this.updatePolicyFormSelectors();

            return true;
        } catch (error) {
            console.error('删除渠道失败:', error);
            throw error;
        }
    }

    /**
     * 删除场景
     */
    async deleteScenario(scenarioId) {
        try {
            const scenario = this.scenarios.get(scenarioId);
            if (!scenario) {
                throw new Error('场景不存在');
            }

            // 检查是否有关联的策略
            const policyCount = this.getScenarioPolicyCount(scenarioId);
            if (policyCount > 0) {
                const confirmed = confirm(`该场景关联了 ${policyCount} 个策略，删除后这些策略将失效，确定继续吗？`);
                if (!confirmed) {
                    return false;
                }
            }

            // 删除场景
            this.scenarios.delete(scenarioId);
            
            // 模拟API调用
            await this.deleteScenarioOnServer(scenarioId);

            // 刷新界面
            this.renderChannelScenarioTree();
            this.updatePolicyFormSelectors();

            return true;
        } catch (error) {
            console.error('删除场景失败:', error);
            throw error;
        }
    }

    /**
     * 更新策略表单中的选择器
     */
    updatePolicyFormSelectors() {
        // 更新渠道选择器
        const channelPicker = document.getElementById('channelPicker');
        if (channelPicker) {
            const currentValue = channelPicker.value;
            channelPicker.innerHTML = '<option value="">选择渠道...</option>';
            
            this.channels.forEach(channel => {
                const option = document.createElement('option');
                option.value = channel.id;
                option.textContent = channel.name;
                channelPicker.appendChild(option);
            });
            
            channelPicker.value = currentValue;
        }

        // 如果有选中的渠道，更新场景选择器
        if (channelPicker && channelPicker.value) {
            this.updateScenarioPickerForChannel(channelPicker.value);
        }
    }

    /**
     * 更新指定渠道的场景选择器
     */
    updateScenarioPickerForChannel(channelId) {
        const scenarioPicker = document.getElementById('scenarioPicker');
        if (!scenarioPicker) return;

        scenarioPicker.innerHTML = '<option value="">选择场景...</option>';
        
        const channelScenarios = Array.from(this.scenarios.values())
            .filter(scenario => scenario.channelId === channelId);

        if (channelScenarios.length > 0) {
            channelScenarios.forEach(scenario => {
                const option = document.createElement('option');
                option.value = scenario.id;
                option.textContent = scenario.name;
                scenarioPicker.appendChild(option);
            });
            scenarioPicker.disabled = false;
        } else {
            scenarioPicker.innerHTML = '<option value="">该渠道暂无场景</option>';
            scenarioPicker.disabled = true;
        }
    }

    /**
     * 验证渠道数据
     */
    validateChannelData(data) {
        if (!data.name || !data.name.trim()) {
            throw new Error('渠道名称不能为空');
        }
        if (!data.code || !data.code.trim()) {
            throw new Error('渠道标识不能为空');
        }
        if (!/^[a-zA-Z0-9_-]+$/.test(data.code)) {
            throw new Error('渠道标识只能包含字母、数字、下划线和连字符');
        }
        return true;
    }

    /**
     * 验证场景数据
     */
    validateScenarioData(data) {
        if (!data.name || !data.name.trim()) {
            throw new Error('场景名称不能为空');
        }
        if (!data.channelId) {
            throw new Error('必须选择所属渠道');
        }
        if (!this.channels.has(data.channelId)) {
            throw new Error('所选渠道不存在');
        }
        if (!data.code || !data.code.trim()) {
            throw new Error('场景标识不能为空');
        }
        if (!/^[a-zA-Z0-9_-]+$/.test(data.code)) {
            throw new Error('场景标识只能包含字母、数字、下划线和连字符');
        }
        
        // 验证自定义阈值
        if (data.thresholdMode === 'custom' && data.customThresholds) {
            const thresholds = data.customThresholds;
            if (thresholds.liveness < 0 || thresholds.liveness > 1) {
                throw new Error('活体分数阈值必须在0-1之间');
            }
            if (thresholds.comparison < 0 || thresholds.comparison > 1) {
                throw new Error('比对分数阈值必须在0-1之间');
            }
            if (thresholds.deepfake < 0 || thresholds.deepfake > 1) {
                throw new Error('Deepfake阈值必须在0-1之间');
            }
        }
        
        return true;
    }

    /**
     * 检查渠道标识是否存在
     */
    isChannelCodeExists(code) {
        return Array.from(this.channels.values()).some(channel => channel.code === code);
    }

    /**
     * 检查场景标识是否存在（在指定渠道下）
     */
    isScenarioCodeExists(code, channelId) {
        return Array.from(this.scenarios.values())
            .some(scenario => scenario.code === code && scenario.channelId === channelId);
    }

    /**
     * 生成ID
     */
    generateId(prefix) {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 获取渠道关联的策略数量（模拟）
     */
    getChannelPolicyCount(channelId) {
        // 模拟数据
        const mockCounts = {
            'mobile-app': 3,
            'mini-program': 1,
            'web': 0
        };
        return mockCounts[channelId] || 0;
    }

    /**
     * 获取场景关联的策略数量（模拟）
     */
    getScenarioPolicyCount(scenarioId) {
        // 模拟数据
        const mockCounts = {
            'user-register': 2,
            'large-transfer': 1,
            'password-reset': 1,
            'user-register-mini': 1
        };
        return mockCounts[scenarioId] || 0;
    }

    /**
     * 获取渠道数据
     */
    getChannel(channelId) {
        return this.channels.get(channelId);
    }

    /**
     * 获取场景数据
     */
    getScenario(scenarioId) {
        return this.scenarios.get(scenarioId);
    }

    /**
     * 获取渠道下的所有场景
     */
    getChannelScenarios(channelId) {
        return Array.from(this.scenarios.values())
            .filter(scenario => scenario.channelId === channelId);
    }

    /**
     * API调用方法（模拟）
     */
    async saveChannelToServer(channel) {
        return new Promise(resolve => setTimeout(resolve, 500));
    }

    async saveScenarioToServer(scenario) {
        return new Promise(resolve => setTimeout(resolve, 500));
    }

    async updateChannelOnServer(channel) {
        return new Promise(resolve => setTimeout(resolve, 500));
    }

    async updateScenarioOnServer(scenario) {
        return new Promise(resolve => setTimeout(resolve, 500));
    }

    async deleteChannelOnServer(channelId) {
        return new Promise(resolve => setTimeout(resolve, 500));
    }

    async deleteScenarioOnServer(scenarioId) {
        return new Promise(resolve => setTimeout(resolve, 500));
    }
}

// 全局实例
window.channelScenarioManager = new ChannelScenarioManager();