/**
 * 策略配置管理器
 * 负责策略的创建、编辑、版本控制等功能
 */
class PolicyConfigManager {
    constructor() {
        this.currentPolicy = null;
        this.isDirty = false;
        this.autoSaveTimer = null;
        this.validationRules = this.initValidationRules();
        this.init();
    }

    /**
     * 初始化管理器
     */
    init() {
        this.bindEvents();
        this.loadChannelsAndScenarios();
        this.initAutoSave();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 表单变化监听
        document.addEventListener('input', (e) => {
            if (e.target.closest('#policyForm')) {
                this.markDirty();
                this.validateField(e.target);
            }
        });

        // 活体模式选择
        document.querySelectorAll('.liveness-mode-card').forEach(card => {
            card.addEventListener('click', (e) => {
                this.selectLivenessMode(card.dataset.mode);
            });
        });

        // 阈值滑块同步
        document.querySelectorAll('.threshold-slider').forEach(slider => {
            slider.addEventListener('input', (e) => {
                this.syncThresholdControls(e.target);
            });
        });

        // 开关切换
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                this.toggleSwitch(e.target);
            });
        });

        // 多选器事件
        this.bindMultiSelectorEvents();
    }

    /**
     * 绑定多选器事件
     */
    bindMultiSelectorEvents() {
        // 渠道选择
        document.querySelector('select[onchange="addChannel(this)"]')?.addEventListener('change', (e) => {
            this.addToMultiSelector('channelSelector', e.target);
        });

        // 场景选择
        document.querySelector('select[onchange="addScenario(this)"]')?.addEventListener('change', (e) => {
            this.addToMultiSelector('scenarioSelector', e.target);
        });

        // 动作选择
        document.querySelector('select[onchange="addAction(this)"]')?.addEventListener('change', (e) => {
            this.addToMultiSelector('actionSelector', e.target);
        });

        // 移除项目事件委托
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('remove')) {
                e.target.parentElement.remove();
                this.markDirty();
            }
        });
    }

    /**
     * 选择活体模式
     */
    selectLivenessMode(mode) {
        // 更新UI状态
        document.querySelectorAll('.liveness-mode-card').forEach(card => {
            card.classList.remove('selected');
        });
        document.querySelector(`[data-mode="${mode}"]`).classList.add('selected');

        // 显示/隐藏交互配置面板
        const interactiveConfig = document.getElementById('interactiveConfig');
        if (mode === 'interactive') {
            interactiveConfig.classList.remove('hidden');
        } else {
            interactiveConfig.classList.add('hidden');
        }

        this.markDirty();
    }

    /**
     * 同步阈值控件
     */
    syncThresholdControls(slider) {
        const container = slider.parentElement;
        const input = container.querySelector('.threshold-input');
        const preview = container.querySelector('.threshold-preview');
        
        input.value = slider.value;
        this.updateThresholdPreview(slider.value, this.getThresholdType(slider), preview);
        this.markDirty();
    }

    /**
     * 获取阈值类型
     */
    getThresholdType(element) {
        const oninput = element.getAttribute('oninput');
        if (oninput.includes('liveness')) return 'liveness';
        if (oninput.includes('comparison')) return 'comparison';
        if (oninput.includes('deepfake')) return 'deepfake';
        return 'unknown';
    }

    /**
     * 更新阈值预览
     */
    updateThresholdPreview(value, type, previewElement) {
        let text = '';
        const numValue = parseFloat(value);
        
        switch(type) {
            case 'liveness':
                const passRate = Math.round((1 - numValue) * 20 + 80);
                const rejectRate = Math.round(numValue * 5);
                text = `当前设置：通过率约${passRate}%，误拒率约${rejectRate}%`;
                break;
            case 'comparison':
                const compPassRate = Math.round((1 - numValue) * 15 + 85);
                const compRejectRate = Math.round(numValue * 3);
                text = `当前设置：通过率约${compPassRate}%，误拒率约${compRejectRate}%`;
                break;
            case 'deepfake':
                const detectRate = Math.round(numValue * 100);
                const falseRate = Math.round((1 - numValue) * 5);
                text = `当前设置：检出率约${detectRate}%，误报率约${falseRate}%`;
                break;
        }
        
        if (previewElement) {
            previewElement.textContent = text;
        }
    }

    /**
     * 切换开关状态
     */
    toggleSwitch(toggle) {
        toggle.classList.toggle('active');
        this.markDirty();
    }

    /**
     * 添加到多选器
     */
    addToMultiSelector(selectorId, selectElement) {
        if (!selectElement.value) return;

        const selector = document.getElementById(selectorId);
        const existingItems = Array.from(selector.querySelectorAll('.multi-selector-item'))
            .map(item => item.textContent.replace('×', '').trim());

        const newText = selectElement.options[selectElement.selectedIndex].text;
        
        // 避免重复添加
        if (existingItems.includes(newText)) {
            selectElement.value = '';
            return;
        }

        const item = document.createElement('div');
        item.className = 'multi-selector-item';
        item.innerHTML = `${newText} <span class="remove">&times;</span>`;
        selector.appendChild(item);
        
        selectElement.value = '';
        this.markDirty();
    }

    /**
     * 标记为已修改
     */
    markDirty() {
        this.isDirty = true;
        this.scheduleAutoSave();
    }

    /**
     * 初始化自动保存
     */
    initAutoSave() {
        // 页面卸载前提醒保存
        window.addEventListener('beforeunload', (e) => {
            if (this.isDirty) {
                e.preventDefault();
                e.returnValue = '您有未保存的更改，确定要离开吗？';
            }
        });
    }

    /**
     * 计划自动保存
     */
    scheduleAutoSave() {
        if (this.autoSaveTimer) {
            clearTimeout(this.autoSaveTimer);
        }
        
        this.autoSaveTimer = setTimeout(() => {
            this.autoSaveDraft();
        }, 30000); // 30秒后自动保存草稿
    }

    /**
     * 自动保存草稿
     */
    async autoSaveDraft() {
        try {
            const formData = this.collectFormData();
            await this.savePolicyDraft(formData);
            console.log('草稿已自动保存');
        } catch (error) {
            console.error('自动保存失败:', error);
        }
    }

    /**
     * 收集表单数据
     */
    collectFormData() {
        const form = document.getElementById('policyForm');
        const formData = new FormData(form);
        
        const policy = {
            name: formData.get('policyName'),
            description: formData.get('policyDescription'),
            channels: this.getMultiSelectorValues('channelSelector'),
            scenarios: this.getMultiSelectorValues('scenarioSelector'),
            livenessMode: this.getSelectedLivenessMode(),
            thresholds: {
                liveness: parseFloat(document.querySelector('[oninput*="liveness"]').value),
                comparison: parseFloat(document.querySelector('[oninput*="comparison"]').value),
                deepfake: parseFloat(document.querySelector('[oninput*="deepfake"]').value)
            },
            qualityControl: this.getQualityControlSettings(),
            dataReturn: this.getDataReturnSettings()
        };

        // 交互活体特殊配置
        if (policy.livenessMode === 'interactive') {
            policy.interactiveConfig = {
                actions: this.getMultiSelectorValues('actionSelector'),
                actionCount: parseInt(formData.get('actionCount')) || 2,
                timeout: parseInt(formData.get('timeout')) || 30
            };
        }

        return policy;
    }

    /**
     * 获取多选器的值
     */
    getMultiSelectorValues(selectorId) {
        const selector = document.getElementById(selectorId);
        return Array.from(selector.querySelectorAll('.multi-selector-item'))
            .map(item => item.textContent.replace('×', '').trim());
    }

    /**
     * 获取选中的活体模式
     */
    getSelectedLivenessMode() {
        const selected = document.querySelector('.liveness-mode-card.selected');
        return selected ? selected.dataset.mode : 'silent';
    }

    /**
     * 获取质量控制设置
     */
    getQualityControlSettings() {
        const toggles = document.querySelectorAll('.quality-control-item .toggle-switch');
        return {
            allowClosedEyes: toggles[0]?.classList.contains('active') || false,
            allowOpenMouth: toggles[1]?.classList.contains('active') || false,
            allowOcclusion: toggles[2]?.classList.contains('active') || false,
            allowSideface: toggles[3]?.classList.contains('active') || false
        };
    }

    /**
     * 获取数据返回设置
     */
    getDataReturnSettings() {
        const section = document.querySelector('[data-section="data-return"]') || 
                       document.querySelector('.form-section:last-child');
        const toggles = section?.querySelectorAll('.toggle-switch') || [];
        
        return {
            bestFaceImage: toggles[0]?.classList.contains('active') || false,
            fullVideo: toggles[1]?.classList.contains('active') || false,
            processLogs: toggles[2]?.classList.contains('active') || false,
            algorithmScores: toggles[3]?.classList.contains('active') || false
        };
    }

    /**
     * 初始化验证规则
     */
    initValidationRules() {
        return {
            policyName: {
                required: true,
                minLength: 2,
                maxLength: 50,
                pattern: /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/,
                unique: true
            },
            channels: {
                required: true,
                minItems: 1
            },
            scenarios: {
                required: true,
                minItems: 1
            },
            thresholds: {
                liveness: { min: 0, max: 1 },
                comparison: { min: 0, max: 1 },
                deepfake: { min: 0, max: 1 }
            }
        };
    }

    /**
     * 验证字段
     */
    async validateField(field) {
        const name = field.name;
        const value = field.value;
        const rules = this.validationRules[name];
        
        if (!rules) return true;

        const errors = [];

        // 必填验证
        if (rules.required && !value.trim()) {
            errors.push('此字段为必填项');
        }

        // 长度验证
        if (rules.minLength && value.length < rules.minLength) {
            errors.push(`最少需要${rules.minLength}个字符`);
        }
        if (rules.maxLength && value.length > rules.maxLength) {
            errors.push(`最多允许${rules.maxLength}个字符`);
        }

        // 格式验证
        if (rules.pattern && !rules.pattern.test(value)) {
            errors.push('格式不正确');
        }

        // 唯一性验证
        if (rules.unique && name === 'policyName') {
            const isUnique = await this.checkPolicyNameUnique(value);
            if (!isUnique) {
                errors.push('策略名称已存在');
            }
        }

        // 显示错误
        this.showFieldErrors(field, errors);
        
        return errors.length === 0;
    }

    /**
     * 显示字段错误
     */
    showFieldErrors(field, errors) {
        // 移除现有错误提示
        const existingError = field.parentElement.querySelector('.form-error');
        if (existingError) {
            existingError.remove();
        }

        // 添加新错误提示
        if (errors.length > 0) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'form-error';
            errorDiv.textContent = errors[0]; // 只显示第一个错误
            field.parentElement.appendChild(errorDiv);
            
            field.style.borderColor = 'var(--danger-color)';
        } else {
            field.style.borderColor = '';
        }
    }

    /**
     * 验证整个表单
     */
    async validateForm() {
        const formData = this.collectFormData();
        const errors = [];

        // 基本信息验证
        if (!formData.name?.trim()) {
            errors.push('策略名称不能为空');
        }
        if (formData.channels.length === 0) {
            errors.push('至少选择一个渠道');
        }
        if (formData.scenarios.length === 0) {
            errors.push('至少选择一个场景');
        }

        // 交互活体特殊验证
        if (formData.livenessMode === 'interactive') {
            if (!formData.interactiveConfig?.actions?.length) {
                errors.push('交互活体模式需要选择至少一个动作');
            }
            if (formData.interactiveConfig?.actionCount > formData.interactiveConfig?.actions?.length) {
                errors.push('动作数量不能超过选择的动作种类');
            }
        }

        // 阈值验证
        const thresholds = formData.thresholds;
        if (thresholds.liveness < 0 || thresholds.liveness > 1) {
            errors.push('活体分数阈值必须在0-1之间');
        }
        if (thresholds.comparison < 0 || thresholds.comparison > 1) {
            errors.push('比对分数阈值必须在0-1之间');
        }
        if (thresholds.deepfake < 0 || thresholds.deepfake > 1) {
            errors.push('Deepfake阈值必须在0-1之间');
        }

        return errors;
    }

    /**
     * 保存策略
     */
    async savePolicy() {
        try {
            // 验证表单
            const errors = await this.validateForm();
            if (errors.length > 0) {
                this.showFormErrors(errors);
                return false;
            }

            // 收集数据
            const policyData = this.collectFormData();
            policyData.status = 'active';

            // 发送请求
            const response = await this.savePolicyToServer(policyData);
            
            if (response.success) {
                this.isDirty = false;
                this.showSuccessMessage('策略保存成功');
                this.closePolicyModal();
                this.refreshPolicyList();
                return true;
            } else {
                this.showErrorMessage(response.message || '保存失败');
                return false;
            }
        } catch (error) {
            console.error('保存策略失败:', error);
            this.showErrorMessage('保存失败，请稍后重试');
            return false;
        }
    }

    /**
     * 保存草稿
     */
    async saveDraft() {
        try {
            const policyData = this.collectFormData();
            policyData.status = 'draft';

            const response = await this.savePolicyDraft(policyData);
            
            if (response.success) {
                this.isDirty = false;
                this.showSuccessMessage('草稿保存成功');
                return true;
            } else {
                this.showErrorMessage(response.message || '保存草稿失败');
                return false;
            }
        } catch (error) {
            console.error('保存草稿失败:', error);
            this.showErrorMessage('保存草稿失败，请稍后重试');
            return false;
        }
    }

    /**
     * 加载策略数据到表单
     */
    loadPolicyToForm(policy) {
        // 基本信息
        document.querySelector('[name="policyName"]').value = policy.name || '';
        document.querySelector('[name="policyDescription"]').value = policy.description || '';

        // 渠道和场景
        this.loadMultiSelectorItems('channelSelector', policy.channels || []);
        this.loadMultiSelectorItems('scenarioSelector', policy.scenarios || []);

        // 活体模式
        this.selectLivenessMode(policy.livenessMode || 'silent');

        // 交互活体配置
        if (policy.interactiveConfig) {
            this.loadMultiSelectorItems('actionSelector', policy.interactiveConfig.actions || []);
            document.querySelector('[name="actionCount"]').value = policy.interactiveConfig.actionCount || 2;
            document.querySelector('[name="timeout"]').value = policy.interactiveConfig.timeout || 30;
        }

        // 阈值配置
        if (policy.thresholds) {
            this.setThresholdValue('liveness', policy.thresholds.liveness || 0.8);
            this.setThresholdValue('comparison', policy.thresholds.comparison || 0.75);
            this.setThresholdValue('deepfake', policy.thresholds.deepfake || 0.9);
        }

        // 质量控制
        if (policy.qualityControl) {
            this.setQualityControlSettings(policy.qualityControl);
        }

        // 数据返回
        if (policy.dataReturn) {
            this.setDataReturnSettings(policy.dataReturn);
        }

        this.currentPolicy = policy;
        this.isDirty = false;
    }

    /**
     * 加载多选器项目
     */
    loadMultiSelectorItems(selectorId, items) {
        const selector = document.getElementById(selectorId);
        selector.innerHTML = '';
        
        items.forEach(item => {
            const itemDiv = document.createElement('div');
            itemDiv.className = 'multi-selector-item';
            itemDiv.innerHTML = `${item} <span class="remove">&times;</span>`;
            selector.appendChild(itemDiv);
        });
    }

    /**
     * 设置阈值
     */
    setThresholdValue(type, value) {
        const slider = document.querySelector(`[oninput*="${type}"]`);
        const input = slider.parentElement.querySelector('.threshold-input');
        const preview = slider.parentElement.querySelector('.threshold-preview');
        
        slider.value = value;
        input.value = value;
        this.updateThresholdPreview(value, type, preview);
    }

    /**
     * 设置质量控制
     */
    setQualityControlSettings(settings) {
        const toggles = document.querySelectorAll('.quality-control-item .toggle-switch');
        const keys = ['allowClosedEyes', 'allowOpenMouth', 'allowOcclusion', 'allowSideface'];
        
        keys.forEach((key, index) => {
            if (toggles[index]) {
                if (settings[key]) {
                    toggles[index].classList.add('active');
                } else {
                    toggles[index].classList.remove('active');
                }
            }
        });
    }

    /**
     * 设置数据返回配置
     */
    setDataReturnSettings(settings) {
        const section = document.querySelector('[data-section="data-return"]') || 
                       document.querySelector('.form-section:last-child');
        const toggles = section?.querySelectorAll('.toggle-switch') || [];
        const keys = ['bestFaceImage', 'fullVideo', 'processLogs', 'algorithmScores'];
        
        keys.forEach((key, index) => {
            if (toggles[index]) {
                if (settings[key]) {
                    toggles[index].classList.add('active');
                } else {
                    toggles[index].classList.remove('active');
                }
            }
        });
    }

    /**
     * API调用方法
     */
    async checkPolicyNameUnique(name) {
        // 模拟API调用
        return new Promise(resolve => {
            setTimeout(() => {
                // 这里应该调用实际的API
                resolve(name !== '已存在的策略名称');
            }, 500);
        });
    }

    async savePolicyToServer(policyData) {
        // 模拟API调用
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({ success: true, id: Date.now() });
            }, 1000);
        });
    }

    async savePolicyDraft(policyData) {
        // 模拟API调用
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({ success: true, id: Date.now() });
            }, 500);
        });
    }

    async loadChannelsAndScenarios() {
        // 模拟加载渠道和场景数据
        // 实际应用中应该从API获取
    }

    /**
     * UI辅助方法
     */
    showFormErrors(errors) {
        // 在表单顶部显示错误汇总
        const form = document.getElementById('policyForm');
        let errorContainer = form.querySelector('.form-errors');
        
        if (!errorContainer) {
            errorContainer = document.createElement('div');
            errorContainer.className = 'form-errors';
            errorContainer.style.cssText = `
                background-color: rgba(255, 59, 48, 0.1);
                border: 1px solid var(--danger-color);
                border-radius: 6px;
                padding: 12px;
                margin-bottom: 20px;
                color: var(--danger-color);
            `;
            form.insertBefore(errorContainer, form.firstChild);
        }
        
        errorContainer.innerHTML = `
            <strong>请修正以下错误：</strong>
            <ul style="margin: 8px 0 0 20px;">
                ${errors.map(error => `<li>${error}</li>`).join('')}
            </ul>
        `;
    }

    showSuccessMessage(message) {
        // 显示成功消息
        this.showToast(message, 'success');
    }

    showErrorMessage(message) {
        // 显示错误消息
        this.showToast(message, 'error');
    }

    showToast(message, type = 'info') {
        // 简单的toast实现
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            background-color: ${type === 'success' ? 'var(--success-color)' : 
                              type === 'error' ? 'var(--danger-color)' : 
                              'var(--primary-color)'};
        `;
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    closePolicyModal() {
        document.getElementById('policyModal').classList.remove('show');
    }

    refreshPolicyList() {
        // 刷新策略列表
        window.location.reload();
    }
}

// 全局实例
window.policyConfigManager = new PolicyConfigManager();