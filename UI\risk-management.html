<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSID Platform - 风险管控</title>
    <style>
        :root { --bg-color: #f6f8fb; --sidebar-bg-color: #ffffff; --card-bg-color: #ffffff; --text-color: #1e293b; --text-color-secondary: #64748b; --border-color: #e5eaf3; --primary-color: #2d78f4; --primary-color-light: #f0f6ff; --shadow-color: rgba(0,0,0,0.06); --icon-color: #64748b; --icon-hover-bg: #f1f5f9; --success-color: #4cd964; --warning-color: #ff9500; --danger-color: #ff3b30; }
        [data-theme="dark"] { --bg-color: #111827; --sidebar-bg-color: #1f2937; --card-bg-color: #1f2937; --text-color: #f9fafb; --text-color-secondary: #9ca3af; --border-color: #374151; --primary-color: #3b82f6; --primary-color-light: #252e3d; --shadow-color: rgba(0,0,0,0.2); --icon-color: #9ca3af; --icon-hover-bg: #374151; }
        * { margin: 0; padding: 0; box-sizing: border-box; font-family: "PingFang SC", "Microsoft YaHei", sans-serif; }
        html, body { height: 100%; overflow: hidden; }
        body { background-color: var(--bg-color); color: var(--text-color); transition: background-color 0.3s, color 0.3s; display: flex; }
        .sidebar { width: 260px; background-color: var(--sidebar-bg-color); border-right: 1px solid var(--border-color); display: flex; flex-direction: column; height: 100%; flex-shrink: 0; transition: background-color 0.3s, border-color 0.3s; }
        .main-wrapper { flex-grow: 1; height: 100%; overflow-y: auto; }
        .main-content { padding: 24px; }
        .sidebar-header { display: flex; align-items: center; padding: 0 24px; height: 64px; border-bottom: 1px solid var(--border-color); flex-shrink: 0; }
        .sidebar-logo { font-size: 22px; font-weight: 700; color: var(--primary-color); }
        .sidebar-nav { flex-grow: 1; padding: 16px 0; }
        .nav-list { list-style: none; }
        .nav-item a { display: flex; align-items: center; gap: 12px; padding: 12px 24px; margin: 4px 16px; border-radius: 8px; color: var(--text-color-secondary); text-decoration: none; font-weight: 500; transition: background-color 0.2s, color 0.2s; }
        .nav-item a:hover { background-color: var(--primary-color-light); color: var(--primary-color); }
        .nav-item.active a { background-color: var(--primary-color-light); color: var(--primary-color); font-weight: 600; }
        .nav-icon { width: 20px; height: 20px; }
        .sub-nav-list { list-style: none; margin-left: 16px; max-height: 0; overflow: hidden; transition: max-height 0.3s ease; }
        .nav-item.expanded .sub-nav-list { max-height: 200px; }
        .sub-nav-list .nav-item a { padding: 8px 24px; margin: 2px 16px; font-size: 13px; }
        .sub-nav-list .nav-item a:before { content: "•"; margin-right: 8px; color: var(--text-color-secondary); }
        .nav-item.has-submenu > a { position: relative; }
        .nav-item.has-submenu > a::after { content: "▶"; position: absolute; right: 16px; font-size: 12px; transition: transform 0.3s ease; }
        .nav-item.has-submenu.expanded > a::after { transform: rotate(90deg); }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .page-title { font-size: 24px; font-weight: 700; }
        .header-actions { display: flex; align-items: center; gap: 16px; }
        .theme-toggle { background-color: var(--icon-hover-bg); border: none; border-radius: 50%; width: 36px; height: 36px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: var(--icon-color); transition: background-color 0.2s; }
        .theme-toggle .icon { width: 20px; height: 20px; }
        #moon-icon { display: none; }
        .btn { padding: 8px 16px; border-radius: 6px; border: 1px solid transparent; font-size: 14px; cursor: pointer; font-weight: 500; transition: all 0.2s; display: inline-flex; align-items: center; gap: 6px; }
        .btn-primary { background-color: var(--primary-color); color: white; border-color: var(--primary-color); }
        .btn-secondary { background-color: var(--card-bg-color); color: var(--text-color); border-color: var(--border-color); }
        .btn-danger { background-color: var(--danger-color); color: white; border-color: var(--danger-color); }
        .btn-sm { padding: 4px 10px; font-size: 12px; }
        .card { background-color: var(--card-bg-color); border-radius: 12px; border: 1px solid var(--border-color); box-shadow: 0 1px 3px var(--shadow-color); margin-bottom: 24px; transition: background-color 0.3s, border-color 0.3s; }
        .card-header { padding: 20px 24px 0; border-bottom: none; }
        .card-title { font-size: 18px; font-weight: 600; }
        .card-content { padding: 20px 24px; }
        
        /* 风险管控专用样式 */
        .risk-overview { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin-bottom: 24px; }
        .risk-stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 24px; border-radius: 12px; position: relative; overflow: hidden; }
        .risk-stat-card.high-risk { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); }
        .risk-stat-card.medium-risk { background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%); }
        .risk-stat-card.low-risk { background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%); }
        .risk-stat-value { font-size: 32px; font-weight: 700; margin-bottom: 8px; }
        .risk-stat-label { font-size: 14px; opacity: 0.9; margin-bottom: 12px; }
        .risk-stat-trend { font-size: 12px; opacity: 0.8; }
        

        
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 12px 16px; text-align: left; border-bottom: 1px solid var(--border-color); }
        .table th { background-color: var(--bg-color); font-weight: 600; font-size: 13px; color: var(--text-color-secondary); }
        .table tbody tr:hover { background-color: var(--bg-color); }
        
        .risk-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .risk-high { background-color: rgba(255, 59, 48, 0.1); color: var(--danger-color); }
        .risk-medium { background-color: rgba(255, 149, 0, 0.1); color: var(--warning-color); }
        .risk-low { background-color: rgba(76, 217, 100, 0.1); color: var(--success-color); }
        
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-active { background-color: rgba(76, 217, 100, 0.1); color: var(--success-color); }
        .status-inactive { background-color: rgba(100, 116, 139, 0.1); color: var(--text-color-secondary); }
        .status-pending { background-color: rgba(255, 149, 0, 0.1); color: var(--warning-color); }
        
        .action-buttons { display: flex; gap: 8px; }
        .action-buttons .btn { padding: 4px 8px; font-size: 12px; }
        
        /* 实时事件流 */
        .event-stream { max-height: 400px; overflow-y: auto; }
        .event-item { padding: 12px 16px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center; transition: background-color 0.2s; }
        .event-item:last-child { border-bottom: none; }
        .event-item:hover { background-color: var(--bg-color); }
        .event-item.new { animation: highlight 2s ease-out; }
        @keyframes highlight { 0% { background-color: var(--primary-color-light); } 100% { background-color: transparent; } }
        .event-info { flex-grow: 1; }
        .event-time { font-size: 12px; color: var(--text-color-secondary); margin-bottom: 4px; }
        .event-desc { font-size: 14px; margin-bottom: 2px; }
        .event-user { font-size: 12px; color: var(--text-color-secondary); }
        .event-status { font-size: 12px; padding: 2px 6px; border-radius: 10px; margin-left: 8px; }
        .event-status.detecting { background-color: rgba(255, 149, 0, 0.1); color: var(--warning-color); }
        .event-status.blocked { background-color: rgba(255, 59, 48, 0.1); color: var(--danger-color); }

        /* 分页样式 */
        .pagination { display: flex; justify-content: center; align-items: center; gap: 8px; padding: 16px 0; }
        .pagination-btn { padding: 6px 12px; border: 1px solid var(--border-color); background-color: var(--card-bg-color); color: var(--text-color); border-radius: 4px; cursor: pointer; font-size: 14px; transition: all 0.2s; }
        .pagination-btn:hover { border-color: var(--primary-color); color: var(--primary-color); }
        .pagination-btn.active { background-color: var(--primary-color); color: white; border-color: var(--primary-color); }
        .pagination-btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .pagination-info { font-size: 14px; color: var(--text-color-secondary); margin: 0 16px; }
        
        /* 模态框样式 */
        .modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); display: none; align-items: center; justify-content: center; z-index: 1000; }
        .modal.active { display: flex; }
        .modal-content { background-color: var(--card-bg-color); border-radius: 12px; width: 95%; max-width: 1000px; max-height: 95vh; overflow-y: auto; }
        .modal-header { padding: 24px 24px 0; border-bottom: 1px solid var(--border-color); margin-bottom: 24px; }
        .modal-title { font-size: 20px; font-weight: 600; margin-bottom: 16px; }
        .modal-close { position: absolute; top: 16px; right: 16px; background: none; border: none; font-size: 24px; cursor: pointer; color: var(--text-color-secondary); }
        .modal-body { padding: 0 24px 24px; }
        .modal-footer { padding: 16px 24px; border-top: 1px solid var(--border-color); display: flex; justify-content: flex-end; gap: 12px; }

        /* 表单样式 */
        .form-group { margin-bottom: 20px; }
        .form-label { display: block; margin-bottom: 8px; font-weight: 500; color: var(--text-color); }
        .form-input, .form-select, .form-textarea { width: 100%; padding: 10px 12px; border: 1px solid var(--border-color); border-radius: 6px; background-color: var(--card-bg-color); color: var(--text-color); font-size: 14px; transition: border-color 0.2s; }
        .form-input:focus, .form-select:focus, .form-textarea:focus { outline: none; border-color: var(--primary-color); }
        .form-textarea { resize: vertical; min-height: 80px; }
        .form-help { font-size: 12px; color: var(--text-color-secondary); margin-top: 4px; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 16px; }

        /* 条件构建器 */
        .condition-builder { border: 1px solid var(--border-color); border-radius: 8px; padding: 20px; background-color: var(--bg-color); }
        .condition-tabs { display: flex; margin-bottom: 20px; border-bottom: 1px solid var(--border-color); }
        .condition-tab { padding: 12px 20px; cursor: pointer; border-bottom: 2px solid transparent; color: var(--text-color-secondary); font-weight: 500; transition: all 0.2s; }
        .condition-tab.active { color: var(--primary-color); border-bottom-color: var(--primary-color); }
        .condition-tab:hover { color: var(--primary-color); }

        .condition-panel { display: none; }
        .condition-panel.active { display: block; }

        .condition-group { margin-bottom: 16px; padding: 16px; border: 1px solid var(--border-color); border-radius: 8px; background-color: var(--card-bg-color); }
        .condition-group:last-child { margin-bottom: 0; }
        .condition-group-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; }
        .condition-group-title { font-weight: 600; color: var(--text-color); }
        .condition-group-logic { display: flex; align-items: center; gap: 8px; }
        .logic-select { padding: 4px 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px; }

        .condition-row { display: grid; grid-template-columns: 200px 120px 1fr 80px; gap: 12px; align-items: center; margin-bottom: 12px; }
        .condition-row:last-child { margin-bottom: 0; }

        /* 设备风险项特殊样式 */
        .device-risk-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 16px; }
        .device-risk-card { border: 1px solid var(--border-color); border-radius: 8px; padding: 16px; background-color: var(--card-bg-color); }
        .device-risk-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; }
        .device-risk-title { font-weight: 600; color: var(--text-color); font-size: 14px; }
        .device-risk-toggle { width: 36px; height: 20px; background-color: var(--border-color); border-radius: 10px; position: relative; cursor: pointer; transition: background-color 0.3s; }
        .device-risk-toggle.active { background-color: var(--success-color); }
        .device-risk-toggle::after { content: ''; position: absolute; top: 2px; left: 2px; width: 16px; height: 16px; background-color: white; border-radius: 50%; transition: transform 0.3s; }
        .device-risk-toggle.active::after { transform: translateX(16px); }
        .device-risk-desc { font-size: 12px; color: var(--text-color-secondary); margin-bottom: 12px; }
        .device-risk-config { display: none; }
        .device-risk-config.active { display: block; }
        .device-risk-option { display: flex; align-items: center; gap: 8px; margin-bottom: 8px; }
        .device-risk-option:last-child { margin-bottom: 0; }
        .device-risk-checkbox { width: 16px; height: 16px; }
        .device-risk-label { font-size: 13px; color: var(--text-color); }

        .condition-remove { background: var(--danger-color); color: white; border: none; border-radius: 4px; padding: 6px 8px; cursor: pointer; font-size: 12px; }
        .add-condition-btn { background: var(--primary-color-light); color: var(--primary-color); border: 1px solid var(--primary-color); border-radius: 6px; padding: 8px 16px; cursor: pointer; font-size: 14px; margin-top: 12px; }
        .add-group-btn { background: var(--card-bg-color); color: var(--text-color); border: 1px solid var(--border-color); border-radius: 6px; padding: 8px 16px; cursor: pointer; font-size: 14px; margin-top: 12px; margin-left: 12px; }
        
        /* 规则状态指示器 */
        .rule-status { display: flex; align-items: center; gap: 8px; }
        .rule-toggle { width: 40px; height: 22px; background-color: var(--border-color); border-radius: 11px; position: relative; cursor: pointer; transition: background-color 0.3s; }
        .rule-toggle.active { background-color: var(--success-color); }
        .rule-toggle::after { content: ''; position: absolute; top: 2px; left: 2px; width: 18px; height: 18px; background-color: white; border-radius: 50%; transition: transform 0.3s; }
        .rule-toggle.active::after { transform: translateX(18px); }
        
        .live-indicator { display: flex; align-items: center; gap: 8px; font-size: 12px; color: var(--success-color); }
        .pulse-dot { width: 8px; height: 8px; background-color: var(--success-color); border-radius: 50%; animation: pulse 2s infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.3; } }
    </style>
</head>
<body data-theme="light">

    <aside class="sidebar">
        <div class="sidebar-header"><h1 class="sidebar-logo">SSID Platform</h1></div>
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item"><a href="dashboard.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path></svg><span>首页</span></a></li>
                <li class="nav-item"><a href="configuration.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg><span>配置管理</span></a></li>
                <li class="nav-item"><a href="analytics.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg><span>数据统计</span></a></li>
                <li class="nav-item active"><a href="risk-management_v2.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg><span>风险管控</span></a></li>
                <li class="nav-item"><a href="case-audit.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg><span>案例审计</span></a></li>
                <li class="nav-item has-submenu" onclick="toggleSubmenu(this)">
                    <a href="javascript:void(0)">
                        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path></svg>
                        <span>特征库管理</span>
                    </a>
                    <ul class="sub-nav-list">
                        <li class="nav-item"><a href="face-feature-library.html">人脸特征库</a></li>
                        <li class="nav-item"><a href="background-fingerprint-library.html">背景特征库</a></li>
                    </ul>
                </li>
                <li class="nav-item"><a href="distribution.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path></svg><span>资源分发</span></a></li>
                <li class="nav-item"><a href="settings.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 16v-2m8-8h2M4 12H2m15.364 6.364l-1.414-1.414M6.05 6.05L4.636 4.636m12.728 12.728L15.95 15.95M6.05 17.95l1.414-1.414M12 18a6 6 0 100-12 6 6 0 000 12z"></path></svg><span>系统管理</span></a></li>
            </ul>
        </nav>
    </aside>

    <div class="main-wrapper">
        <main class="main-content">
            <header class="page-header">
                <h1 class="page-title">风险管控</h1>
                <div class="header-actions">
                    <button class="btn btn-secondary">导出报告</button>
                    <button class="btn btn-primary" onclick="openNewRuleModal()">+ 新建规则</button>
                    <button class="theme-toggle" id="theme-toggle-btn" title="切换亮/暗模式"><svg id="sun-icon" class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path></svg><svg id="moon-icon" class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path></svg></button>
                </div>
            </header>

            <!-- 风险概览 -->
            <div class="risk-overview">
                <div class="risk-stat-card high-risk">
                    <div class="risk-stat-value">23</div>
                    <div class="risk-stat-label">高风险事件</div>
                    <div class="risk-stat-trend">↑ 15% 较昨日</div>
                </div>
                <div class="risk-stat-card medium-risk">
                    <div class="risk-stat-value">156</div>
                    <div class="risk-stat-label">中风险事件</div>
                    <div class="risk-stat-trend">↓ 8% 较昨日</div>
                </div>
                <div class="risk-stat-card low-risk">
                    <div class="risk-stat-value">1,247</div>
                    <div class="risk-stat-label">低风险事件</div>
                    <div class="risk-stat-trend">→ 持平</div>
                </div>

            </div>

            <!-- 风险规则管理 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">风险规则管理</h2>
                </div>
                <div class="card-content" style="padding: 0;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>规则名称</th>
                                <th>风险等级</th>
                                <th>触发条件</th>
                                <th>响应动作</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>模拟器检测拦截</td>
                                <td><span class="risk-badge risk-high">高风险</span></td>
                                <td>设备类型 = 模拟器</td>
                                <td>直接拒绝</td>
                                <td>
                                    <div class="rule-status">
                                        <div class="rule-toggle active" onclick="toggleRule(this)"></div>
                                        <span>已启用</span>
                                    </div>
                                </td>
                                <td class="action-buttons">
                                    <button class="btn btn-secondary" onclick="editRule('rule_001')">编辑</button>
                                    <button class="btn btn-secondary" onclick="viewRuleDetail('rule_001')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>高频请求限制</td>
                                <td><span class="risk-badge risk-medium">中风险</span></td>
                                <td>1分钟内请求 > 5次</td>
                                <td>提升验证难度</td>
                                <td>
                                    <div class="rule-status">
                                        <div class="rule-toggle active" onclick="toggleRule(this)"></div>
                                        <span>已启用</span>
                                    </div>
                                </td>
                                <td class="action-buttons">
                                    <button class="btn btn-secondary" onclick="editRule('rule_002')">编辑</button>
                                    <button class="btn btn-secondary" onclick="viewRuleDetail('rule_002')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>异地登录告警</td>
                                <td><span class="risk-badge risk-medium">中风险</span></td>
                                <td>IP地理位置异常变化</td>
                                <td>发送告警</td>
                                <td>
                                    <div class="rule-status">
                                        <div class="rule-toggle active" onclick="toggleRule(this)"></div>
                                        <span>已启用</span>
                                    </div>
                                </td>
                                <td class="action-buttons">
                                    <button class="btn btn-secondary" onclick="editRule('rule_003')">编辑</button>
                                    <button class="btn btn-secondary" onclick="viewRuleDetail('rule_003')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>设备黑名单拦截</td>
                                <td><span class="risk-badge risk-high">高风险</span></td>
                                <td>设备指纹在黑名单中</td>
                                <td>直接拒绝</td>
                                <td>
                                    <div class="rule-status">
                                        <div class="rule-toggle" onclick="toggleRule(this)"></div>
                                        <span>已停用</span>
                                    </div>
                                </td>
                                <td class="action-buttons">
                                    <button class="btn btn-secondary" onclick="editRule('rule_004')">编辑</button>
                                    <button class="btn btn-secondary" onclick="viewRuleDetail('rule_004')">详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="pagination">
                    <button class="pagination-btn" onclick="changeRulePage('prev')" id="rulePrevBtn">上一页</button>
                    <button class="pagination-btn active" onclick="goToRulePage(1)">1</button>
                    <button class="pagination-btn" onclick="goToRulePage(2)">2</button>
                    <span class="pagination-info">共 <span id="totalRules">24</span> 条规则，第 <span id="currentRulePage">1</span> 页，共 <span id="totalRulePages">3</span> 页</span>
                    <button class="pagination-btn" onclick="changeRulePage('next')" id="ruleNextBtn">下一页</button>
                </div>
            </div>



            <!-- 风险处置记录 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">风险处置记录</h2>
                    <div style="display: flex; gap: 12px; flex-wrap: wrap; margin-top: 16px; align-items: center;">
                        <select class="btn btn-secondary" style="padding: 6px 12px;" id="riskTypeFilter">
                            <option value="">全部风险类型</option>
                            <option value="模拟器检测">模拟器检测</option>
                            <option value="高频请求">高频请求</option>
                            <option value="异地访问">异地访问</option>
                            <option value="黑名单设备">黑名单设备</option>
                            <option value="行为异常">行为异常</option>
                        </select>
                        <select class="btn btn-secondary" style="padding: 6px 12px;" id="triggerRuleFilter">
                            <option value="">全部触发规则</option>
                            <option value="模拟器检测拦截">模拟器检测拦截</option>
                            <option value="高频请求限制">高频请求限制</option>
                            <option value="异地登录告警">异地登录告警</option>
                            <option value="设备黑名单拦截">设备黑名单拦截</option>
                        </select>
                        <select class="btn btn-secondary" style="padding: 6px 12px;" id="responseActionFilter">
                            <option value="">全部响应动作</option>
                            <option value="直接拒绝">直接拒绝</option>
                            <option value="提升验证难度">提升验证难度</option>
                            <option value="仅记录日志">仅记录日志</option>
                            <option value="发送告警">发送告警</option>
                        </select>
                        <input type="date" class="btn btn-secondary" style="padding: 6px 12px;" id="startDate" title="开始日期">
                        <input type="date" class="btn btn-secondary" style="padding: 6px 12px;" id="endDate" title="结束日期">
                        <button class="btn btn-primary" onclick="applyFilters()">筛选</button>
                        <button class="btn btn-secondary" onclick="resetFilters()">重置</button>
                    </div>
                </div>
                <div class="card-content" style="padding: 0;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>风险类型</th>
                                <th>用户/设备</th>
                                <th>风险等级</th>
                                <th>触发规则</th>
                                <th>响应动作</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>11:23:45</td>
                                <td>模拟器检测</td>
                                <td>user_11****34</td>
                                <td><span class="risk-badge risk-high">高风险</span></td>
                                <td>模拟器检测拦截</td>
                                <td>直接拒绝</td>
                                <td class="action-buttons">
                                    <button class="btn btn-secondary" onclick="goToCaseAudit('case_001')">人工审查</button>
                                    <button class="btn btn-secondary" onclick="viewRecordDetail('record_001')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>11:22:18</td>
                                <td>高频请求</td>
                                <td>ff4e...c1a0</td>
                                <td><span class="risk-badge risk-medium">中风险</span></td>
                                <td>高频请求限制</td>
                                <td>提升验证难度</td>
                                <td class="action-buttons">
                                    <button class="btn btn-secondary" onclick="goToCaseAudit('case_002')">人工审查</button>
                                    <button class="btn btn-secondary" onclick="viewRecordDetail('record_002')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>11:21:02</td>
                                <td>异地访问</td>
                                <td>*************</td>
                                <td><span class="risk-badge risk-medium">中风险</span></td>
                                <td>异地登录告警</td>
                                <td>发送告警</td>
                                <td class="action-buttons">
                                    <button class="btn btn-secondary" onclick="goToCaseAudit('case_003')">人工审查</button>
                                    <button class="btn btn-secondary" onclick="viewRecordDetail('record_003')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>11:19:45</td>
                                <td>黑名单设备</td>
                                <td>a2b1...d5f8</td>
                                <td><span class="risk-badge risk-high">高风险</span></td>
                                <td>设备黑名单拦截</td>
                                <td>直接拒绝</td>
                                <td class="action-buttons">
                                    <button class="btn btn-secondary" onclick="goToCaseAudit('case_004')">人工审查</button>
                                    <button class="btn btn-secondary" onclick="viewRecordDetail('record_004')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>11:18:33</td>
                                <td>行为异常</td>
                                <td>user_9a****f1</td>
                                <td><span class="risk-badge risk-medium">中风险</span></td>
                                <td>异常行为检测</td>
                                <td>仅记录日志</td>
                                <td class="action-buttons">
                                    <button class="btn btn-secondary" onclick="goToCaseAudit('case_005')">人工审查</button>
                                    <button class="btn btn-secondary" onclick="viewRecordDetail('record_005')">详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="pagination">
                    <button class="pagination-btn" onclick="changePage('prev')" id="prevBtn">上一页</button>
                    <button class="pagination-btn active" onclick="goToPage(1)">1</button>
                    <button class="pagination-btn" onclick="goToPage(2)">2</button>
                    <button class="pagination-btn" onclick="goToPage(3)">3</button>
                    <span class="pagination-info">共 <span id="totalRecords">127</span> 条记录，第 <span id="currentPage">1</span> 页，共 <span id="totalPages">13</span> 页</span>
                    <button class="pagination-btn" onclick="changePage('next')" id="nextBtn">下一页</button>
                </div>
            </div>
        </main>
    </div>

    <!-- 新建规则模态框 -->
    <div id="newRuleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">新建风险规则</h2>
                <button class="modal-close" onclick="closeNewRuleModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="newRuleForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">规则名称 <span style="color: var(--danger-color);">*</span></label>
                            <input type="text" class="form-input" id="ruleName" placeholder="请输入规则名称" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">风险等级 <span style="color: var(--danger-color);">*</span></label>
                            <select class="form-select" id="riskLevel" required>
                                <option value="">请选择风险等级</option>
                                <option value="high">高风险</option>
                                <option value="medium">中风险</option>
                                <option value="low">低风险</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">规则描述</label>
                        <textarea class="form-textarea" id="ruleDescription" placeholder="请描述此规则的用途和触发场景"></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label">触发条件 <span style="color: var(--danger-color);">*</span></label>
                        <div class="condition-builder">
                            <div class="condition-tabs">
                                <div class="condition-tab active" onclick="switchConditionTab('device')">设备风险检测</div>
                                <div class="condition-tab" onclick="switchConditionTab('behavior')">行为风险检测</div>
                                <div class="condition-tab" onclick="switchConditionTab('environment')">环境风险检测</div>
                            </div>

                            <!-- 设备风险检测面板 -->
                            <div id="devicePanel" class="condition-panel active">
                                <div class="device-risk-grid">
                                    <div class="device-risk-card">
                                        <div class="device-risk-header">
                                            <div class="device-risk-title">Root/越狱检测</div>
                                            <div class="device-risk-toggle" onclick="toggleDeviceRisk(this, 'root')"></div>
                                        </div>
                                        <div class="device-risk-desc">检测设备是否已获取Root权限或越狱</div>
                                        <div class="device-risk-config">
                                            <div class="device-risk-option">
                                                <input type="checkbox" class="device-risk-checkbox" id="root_detected" checked>
                                                <label class="device-risk-label" for="root_detected">检测到Root/越狱时触发</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="device-risk-card">
                                        <div class="device-risk-header">
                                            <div class="device-risk-title">模拟器检测</div>
                                            <div class="device-risk-toggle" onclick="toggleDeviceRisk(this, 'emulator')"></div>
                                        </div>
                                        <div class="device-risk-desc">检测设备是否为模拟器环境</div>
                                        <div class="device-risk-config">
                                            <div class="device-risk-option">
                                                <input type="checkbox" class="device-risk-checkbox" id="emulator_detected" checked>
                                                <label class="device-risk-label" for="emulator_detected">检测到模拟器时触发</label>
                                            </div>
                                        </div>
                                    </div>



                                    <div class="device-risk-card">
                                        <div class="device-risk-header">
                                            <div class="device-risk-title">VPN检测</div>
                                            <div class="device-risk-toggle" onclick="toggleDeviceRisk(this, 'vpn')"></div>
                                        </div>
                                        <div class="device-risk-desc">检测设备是否使用VPN或代理</div>
                                        <div class="device-risk-config">
                                            <div class="device-risk-option">
                                                <input type="checkbox" class="device-risk-checkbox" id="vpn_detected" checked>
                                                <label class="device-risk-label" for="vpn_detected">检测到VPN连接时触发</label>
                                            </div>
                                            <div class="device-risk-option">
                                                <input type="checkbox" class="device-risk-checkbox" id="proxy_detected" checked>
                                                <label class="device-risk-label" for="proxy_detected">检测到代理连接时触发</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="device-risk-card">
                                        <div class="device-risk-header">
                                            <div class="device-risk-title">USB调试检测</div>
                                            <div class="device-risk-toggle" onclick="toggleDeviceRisk(this, 'usb')"></div>
                                        </div>
                                        <div class="device-risk-desc">检测设备USB调试状态</div>
                                        <div class="device-risk-config">
                                            <div class="device-risk-option">
                                                <input type="checkbox" class="device-risk-checkbox" id="usb_debug_enabled" checked>
                                                <label class="device-risk-label" for="usb_debug_enabled">USB调试已开启时触发</label>
                                            </div>
                                            <div class="device-risk-option">
                                                <input type="checkbox" class="device-risk-checkbox" id="usb_connected" checked>
                                                <label class="device-risk-label" for="usb_connected">USB线缆连接时触发</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="device-risk-card">
                                        <div class="device-risk-header">
                                            <div class="device-risk-title">投屏检测</div>
                                            <div class="device-risk-toggle" onclick="toggleDeviceRisk(this, 'screen')"></div>
                                        </div>
                                        <div class="device-risk-desc">检测设备是否正在投屏</div>
                                        <div class="device-risk-config">
                                            <div class="device-risk-option">
                                                <input type="checkbox" class="device-risk-checkbox" id="screen_mirroring" checked>
                                                <label class="device-risk-label" for="screen_mirroring">检测到投屏时触发</label>
                                            </div>
                                            <div class="device-risk-option">
                                                <input type="checkbox" class="device-risk-checkbox" id="screen_recording" checked>
                                                <label class="device-risk-label" for="screen_recording">检测到录屏时触发</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 行为风险检测面板 -->
                            <div id="behaviorPanel" class="condition-panel">
                                <div id="behaviorConditionGroups">
                                    <div class="condition-group">
                                        <div class="condition-group-header">
                                            <div class="condition-group-title">行为条件组 1</div>
                                            <div class="condition-group-logic">
                                                <span>条件关系:</span>
                                                <select class="logic-select">
                                                    <option value="AND">AND</option>
                                                    <option value="OR">OR</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="condition-row">
                                            <select class="form-select">
                                                <option value="">选择字段</option>
                                                <option value="request_count">请求次数</option>
                                                <option value="request_interval">请求间隔</option>
                                                <option value="failed_attempts">失败次数</option>
                                                <option value="session_duration">会话时长</option>
                                                <option value="user_agent_change">UA变化</option>
                                            </select>
                                            <select class="form-select">
                                                <option value="">操作符</option>
                                                <option value="greater_than">大于</option>
                                                <option value="less_than">小于</option>
                                                <option value="equals">等于</option>
                                                <option value="between">介于</option>
                                            </select>
                                            <input type="text" class="form-input" placeholder="输入值">
                                            <button type="button" class="condition-remove" onclick="removeCondition(this)">删除</button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="add-condition-btn" onclick="addBehaviorCondition()">+ 添加条件</button>
                                <button type="button" class="add-group-btn" onclick="addBehaviorGroup()">+ 添加条件组</button>
                            </div>

                            <!-- 环境风险检测面板 -->
                            <div id="environmentPanel" class="condition-panel">
                                <!-- 环境风险项卡片 -->
                                <div class="device-risk-grid" style="margin-bottom: 24px;">
                                    <div class="device-risk-card">
                                        <div class="device-risk-header">
                                            <div class="device-risk-title">系统版本检测</div>
                                            <div class="device-risk-toggle" onclick="toggleDeviceRisk(this, 'version')"></div>
                                        </div>
                                        <div class="device-risk-desc">检测设备系统版本是否过低</div>
                                        <div class="device-risk-config">
                                            <div class="device-risk-option">
                                                <input type="checkbox" class="device-risk-checkbox" id="ios_low_version" checked>
                                                <label class="device-risk-label" for="ios_low_version">iOS版本低于12.0</label>
                                            </div>
                                            <div class="device-risk-option">
                                                <input type="checkbox" class="device-risk-checkbox" id="android_low_version" checked>
                                                <label class="device-risk-label" for="android_low_version">Android版本低于8.0</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="device-risk-card">
                                        <div class="device-risk-header">
                                            <div class="device-risk-title">地理位置异常</div>
                                            <div class="device-risk-toggle" onclick="toggleDeviceRisk(this, 'location')"></div>
                                        </div>
                                        <div class="device-risk-desc">检测异地登录和位置跳跃</div>
                                        <div class="device-risk-config">
                                            <div class="device-risk-option">
                                                <input type="checkbox" class="device-risk-checkbox" id="location_jump" checked>
                                                <label class="device-risk-label" for="location_jump">短时间内位置跳跃</label>
                                            </div>
                                            <div class="device-risk-option">
                                                <input type="checkbox" class="device-risk-checkbox" id="foreign_location" checked>
                                                <label class="device-risk-label" for="foreign_location">海外IP访问</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="device-risk-card">
                                        <div class="device-risk-header">
                                            <div class="device-risk-title">网络环境检测</div>
                                            <div class="device-risk-toggle" onclick="toggleDeviceRisk(this, 'network')"></div>
                                        </div>
                                        <div class="device-risk-desc">检测网络类型和连接质量</div>
                                        <div class="device-risk-config">
                                            <div class="device-risk-option">
                                                <input type="checkbox" class="device-risk-checkbox" id="public_wifi" checked>
                                                <label class="device-risk-label" for="public_wifi">公共WiFi连接</label>
                                            </div>
                                            <div class="device-risk-option">
                                                <input type="checkbox" class="device-risk-checkbox" id="unstable_network" checked>
                                                <label class="device-risk-label" for="unstable_network">网络连接不稳定</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 自定义环境条件 -->
                                <div style="border-top: 1px solid var(--border-color); padding-top: 20px;">
                                    <h4 style="margin-bottom: 16px; color: var(--text-color);">自定义环境条件</h4>
                                    <div id="environmentConditionGroups">
                                        <div class="condition-group">
                                            <div class="condition-group-header">
                                                <div class="condition-group-title">环境条件组 1</div>
                                                <div class="condition-group-logic">
                                                    <span>条件关系:</span>
                                                    <select class="logic-select">
                                                        <option value="AND">AND</option>
                                                        <option value="OR">OR</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="condition-row">
                                                <select class="form-select">
                                                    <option value="">选择字段</option>
                                                    <option value="ip_address">IP地址</option>
                                                    <option value="location">地理位置</option>
                                                    <option value="network_type">网络类型</option>
                                                    <option value="time_zone">时区</option>
                                                    <option value="language">系统语言</option>
                                                    <option value="screen_resolution">屏幕分辨率</option>
                                                    <option value="device_model">设备型号</option>
                                                </select>
                                                <select class="form-select">
                                                    <option value="">操作符</option>
                                                    <option value="equals">等于</option>
                                                    <option value="not_equals">不等于</option>
                                                    <option value="contains">包含</option>
                                                    <option value="in_list">在列表中</option>
                                                    <option value="not_in_list">不在列表中</option>
                                                </select>
                                                <input type="text" class="form-input" placeholder="输入值">
                                                <button type="button" class="condition-remove" onclick="removeCondition(this)">删除</button>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" class="add-condition-btn" onclick="addEnvironmentCondition()">+ 添加条件</button>
                                    <button type="button" class="add-group-btn" onclick="addEnvironmentGroup()">+ 添加条件组</button>
                                </div>
                            </div>
                        </div>
                        <div class="form-help">设备风险检测：基于设备特征的风险识别；行为风险检测：基于用户行为模式的异常检测；环境风险检测：基于网络环境的风险评估</div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">响应动作 <span style="color: var(--danger-color);">*</span></label>
                            <select class="form-select" id="responseAction" required>
                                <option value="">请选择响应动作</option>
                                <option value="block">直接拒绝</option>
                                <option value="challenge">提升验证难度</option>
                                <option value="log">仅记录日志</option>
                                <option value="alert">发送告警</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">规则状态</label>
                            <select class="form-select" id="ruleStatus">
                                <option value="active">启用</option>
                                <option value="inactive">停用</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">规则优先级</label>
                        <select class="form-select" id="rulePriority">
                            <option value="1">最高优先级</option>
                            <option value="2">高优先级</option>
                            <option value="3" selected>普通优先级</option>
                            <option value="4">低优先级</option>
                        </select>
                        <div class="form-help">优先级高的规则会优先执行</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeNewRuleModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveNewRule()">保存规则</button>
            </div>
        </div>
    </div>

    <!-- 规则详情模态框 -->
    <div id="ruleDetailModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h2 class="modal-title">规则详情</h2>
                <button class="modal-close" onclick="closeRuleDetailModal()">&times;</button>
            </div>
            <div class="modal-body" id="ruleDetailContent">
                <!-- 规则详情内容将在这里动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeRuleDetailModal()">关闭</button>
            </div>
        </div>
    </div>

    <!-- 处置记录详情模态框 -->
    <div id="recordDetailModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h2 class="modal-title">处置记录详情</h2>
                <button class="modal-close" onclick="closeRecordDetailModal()">&times;</button>
            </div>
            <div class="modal-body" id="recordDetailContent">
                <!-- 处置记录详情内容将在这里动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeRecordDetailModal()">关闭</button>
                <button type="button" class="btn btn-primary" onclick="goToCaseAuditFromDetail()">转到案例审计</button>
            </div>
        </div>
    </div>

    <script>
        // 主题切换
        const themeToggleBtn = document.getElementById('theme-toggle-btn');
        const sunIcon = document.getElementById('sun-icon');
        const moonIcon = document.getElementById('moon-icon');
        const body = document.body;
        const applyTheme = (theme) => {
            body.setAttribute('data-theme', theme);
            if (theme === 'dark') { sunIcon.style.display = 'none'; moonIcon.style.display = 'block'; } else { sunIcon.style.display = 'block'; moonIcon.style.display = 'none'; }
        };
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        const defaultTheme = savedTheme || (prefersDark ? 'dark' : 'light');
        applyTheme(defaultTheme);
        themeToggleBtn.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            localStorage.setItem('theme', newTheme);
            applyTheme(newTheme);
        });

        // 规则开关切换
        function toggleRule(toggle) {
            toggle.classList.toggle('active');
            const statusText = toggle.parentElement.querySelector('span');
            if (toggle.classList.contains('active')) {
                statusText.textContent = '已启用';
            } else {
                statusText.textContent = '已停用';
            }
        }

        // 新建规则模态框管理
        function openNewRuleModal() {
            document.getElementById('newRuleModal').classList.add('active');
        }

        function closeNewRuleModal() {
            document.getElementById('newRuleModal').classList.remove('active');
            document.getElementById('newRuleForm').reset();
            // 重置条件构建器
            resetConditionBuilder();
        }

        function resetConditionBuilder() {
            // 重置设备风险检测
            document.querySelectorAll('.device-risk-toggle').forEach(toggle => {
                toggle.classList.remove('active');
                toggle.parentElement.parentElement.querySelector('.device-risk-config').classList.remove('active');
            });

            // 重置行为风险检测
            const behaviorGroups = document.getElementById('behaviorConditionGroups');
            behaviorGroups.innerHTML = `
                <div class="condition-group">
                    <div class="condition-group-header">
                        <div class="condition-group-title">行为条件组 1</div>
                        <div class="condition-group-logic">
                            <span>条件关系:</span>
                            <select class="logic-select">
                                <option value="AND">AND</option>
                                <option value="OR">OR</option>
                            </select>
                        </div>
                    </div>
                    <div class="condition-row">
                        <select class="form-select">
                            <option value="">选择字段</option>
                            <option value="request_count">请求次数</option>
                            <option value="request_interval">请求间隔</option>
                            <option value="failed_attempts">失败次数</option>
                            <option value="session_duration">会话时长</option>
                            <option value="user_agent_change">UA变化</option>
                        </select>
                        <select class="form-select">
                            <option value="">操作符</option>
                            <option value="greater_than">大于</option>
                            <option value="less_than">小于</option>
                            <option value="equals">等于</option>
                            <option value="between">介于</option>
                        </select>
                        <input type="text" class="form-input" placeholder="输入值">
                        <button type="button" class="condition-remove" onclick="removeCondition(this)">删除</button>
                    </div>
                </div>
            `;

            // 重置环境风险检测
            const environmentGroups = document.getElementById('environmentConditionGroups');
            environmentGroups.innerHTML = `
                <div class="condition-group">
                    <div class="condition-group-header">
                        <div class="condition-group-title">环境条件组 1</div>
                        <div class="condition-group-logic">
                            <span>条件关系:</span>
                            <select class="logic-select">
                                <option value="AND">AND</option>
                                <option value="OR">OR</option>
                            </select>
                        </div>
                    </div>
                    <div class="condition-row">
                        <select class="form-select">
                            <option value="">选择字段</option>
                            <option value="ip_address">IP地址</option>
                            <option value="location">地理位置</option>
                            <option value="network_type">网络类型</option>
                            <option value="time_zone">时区</option>
                            <option value="language">系统语言</option>
                            <option value="screen_resolution">屏幕分辨率</option>
                            <option value="device_model">设备型号</option>
                        </select>
                        <select class="form-select">
                            <option value="">操作符</option>
                            <option value="equals">等于</option>
                            <option value="not_equals">不等于</option>
                            <option value="contains">包含</option>
                            <option value="in_list">在列表中</option>
                            <option value="not_in_list">不在列表中</option>
                        </select>
                        <input type="text" class="form-input" placeholder="输入值">
                        <button type="button" class="condition-remove" onclick="removeCondition(this)">删除</button>
                    </div>
                </div>
            `;
        }

        // 条件构建器标签页切换
        function switchConditionTab(tabName) {
            // 切换标签页激活状态
            document.querySelectorAll('.condition-tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');

            // 切换面板显示
            document.querySelectorAll('.condition-panel').forEach(panel => panel.classList.remove('active'));
            document.getElementById(tabName + 'Panel').classList.add('active');
        }

        // 设备风险项开关切换
        function toggleDeviceRisk(toggle, riskType) {
            toggle.classList.toggle('active');
            const config = toggle.parentElement.parentElement.querySelector('.device-risk-config');
            config.classList.toggle('active');
        }

        // 添加行为条件
        function addBehaviorCondition() {
            const groups = document.getElementById('behaviorConditionGroups');
            const lastGroup = groups.lastElementChild;
            const newConditionHtml = `
                <div class="condition-row">
                    <select class="form-select">
                        <option value="">选择字段</option>
                        <option value="request_count">请求次数</option>
                        <option value="request_interval">请求间隔</option>
                        <option value="failed_attempts">失败次数</option>
                        <option value="session_duration">会话时长</option>
                        <option value="user_agent_change">UA变化</option>
                    </select>
                    <select class="form-select">
                        <option value="">操作符</option>
                        <option value="greater_than">大于</option>
                        <option value="less_than">小于</option>
                        <option value="equals">等于</option>
                        <option value="between">介于</option>
                    </select>
                    <input type="text" class="form-input" placeholder="输入值">
                    <button type="button" class="condition-remove" onclick="removeCondition(this)">删除</button>
                </div>
            `;
            lastGroup.insertAdjacentHTML('beforeend', newConditionHtml);
        }

        // 添加行为条件组
        function addBehaviorGroup() {
            const groups = document.getElementById('behaviorConditionGroups');
            const groupCount = groups.children.length + 1;
            const newGroupHtml = `
                <div class="condition-group">
                    <div class="condition-group-header">
                        <div class="condition-group-title">行为条件组 ${groupCount}</div>
                        <div class="condition-group-logic">
                            <span>条件关系:</span>
                            <select class="logic-select">
                                <option value="AND">AND</option>
                                <option value="OR">OR</option>
                            </select>
                        </div>
                    </div>
                    <div class="condition-row">
                        <select class="form-select">
                            <option value="">选择字段</option>
                            <option value="request_count">请求次数</option>
                            <option value="request_interval">请求间隔</option>
                            <option value="failed_attempts">失败次数</option>
                            <option value="session_duration">会话时长</option>
                            <option value="user_agent_change">UA变化</option>
                        </select>
                        <select class="form-select">
                            <option value="">操作符</option>
                            <option value="greater_than">大于</option>
                            <option value="less_than">小于</option>
                            <option value="equals">等于</option>
                            <option value="between">介于</option>
                        </select>
                        <input type="text" class="form-input" placeholder="输入值">
                        <button type="button" class="condition-remove" onclick="removeCondition(this)">删除</button>
                    </div>
                </div>
            `;
            groups.insertAdjacentHTML('beforeend', newGroupHtml);
        }

        // 添加环境条件
        function addEnvironmentCondition() {
            const groups = document.getElementById('environmentConditionGroups');
            const lastGroup = groups.lastElementChild;
            const newConditionHtml = `
                <div class="condition-row">
                    <select class="form-select">
                        <option value="">选择字段</option>
                        <option value="ip_address">IP地址</option>
                        <option value="location">地理位置</option>
                        <option value="network_type">网络类型</option>
                        <option value="time_zone">时区</option>
                        <option value="language">系统语言</option>
                        <option value="screen_resolution">屏幕分辨率</option>
                        <option value="device_model">设备型号</option>
                    </select>
                    <select class="form-select">
                        <option value="">操作符</option>
                        <option value="equals">等于</option>
                        <option value="not_equals">不等于</option>
                        <option value="contains">包含</option>
                        <option value="in_list">在列表中</option>
                        <option value="not_in_list">不在列表中</option>
                    </select>
                    <input type="text" class="form-input" placeholder="输入值">
                    <button type="button" class="condition-remove" onclick="removeCondition(this)">删除</button>
                </div>
            `;
            lastGroup.insertAdjacentHTML('beforeend', newConditionHtml);
        }

        // 添加环境条件组
        function addEnvironmentGroup() {
            const groups = document.getElementById('environmentConditionGroups');
            const groupCount = groups.children.length + 1;
            const newGroupHtml = `
                <div class="condition-group">
                    <div class="condition-group-header">
                        <div class="condition-group-title">环境条件组 ${groupCount}</div>
                        <div class="condition-group-logic">
                            <span>条件关系:</span>
                            <select class="logic-select">
                                <option value="AND">AND</option>
                                <option value="OR">OR</option>
                            </select>
                        </div>
                    </div>
                    <div class="condition-row">
                        <select class="form-select">
                            <option value="">选择字段</option>
                            <option value="ip_address">IP地址</option>
                            <option value="location">地理位置</option>
                            <option value="network_type">网络类型</option>
                            <option value="time_zone">时区</option>
                            <option value="language">系统语言</option>
                            <option value="screen_resolution">屏幕分辨率</option>
                            <option value="device_model">设备型号</option>
                        </select>
                        <select class="form-select">
                            <option value="">操作符</option>
                            <option value="equals">等于</option>
                            <option value="not_equals">不等于</option>
                            <option value="contains">包含</option>
                            <option value="in_list">在列表中</option>
                            <option value="not_in_list">不在列表中</option>
                        </select>
                        <input type="text" class="form-input" placeholder="输入值">
                        <button type="button" class="condition-remove" onclick="removeCondition(this)">删除</button>
                    </div>
                </div>
            `;
            groups.insertAdjacentHTML('beforeend', newGroupHtml);
        }

        // 添加条件（兼容旧版本）
        function addCondition() {
            const conditionGroups = document.getElementById('conditionGroups');
            const newConditionHtml = `
                <div class="condition-group">
                    <div class="condition-row">
                        <select class="form-select condition-select">
                            <option value="">选择字段</option>
                            <option value="device_type">设备类型</option>
                            <option value="ip_address">IP地址</option>
                            <option value="user_id">用户ID</option>
                            <option value="request_count">请求次数</option>
                            <option value="location">地理位置</option>
                            <option value="device_fingerprint">设备指纹</option>
                            <option value="time_interval">时间间隔</option>
                        </select>
                        <select class="form-select condition-select">
                            <option value="">选择操作符</option>
                            <option value="equals">等于</option>
                            <option value="not_equals">不等于</option>
                            <option value="contains">包含</option>
                            <option value="not_contains">不包含</option>
                            <option value="greater_than">大于</option>
                            <option value="less_than">小于</option>
                            <option value="in_list">在列表中</option>
                            <option value="not_in_list">不在列表中</option>
                        </select>
                        <input type="text" class="form-input condition-input" placeholder="输入值">
                        <button type="button" class="condition-remove" onclick="removeCondition(this)">删除</button>
                    </div>
                </div>
            `;
            conditionGroups.insertAdjacentHTML('beforeend', newConditionHtml);
        }

        // 删除条件
        function removeCondition(button) {
            const conditionGroup = button.closest('.condition-group');
            const conditionGroups = document.getElementById('conditionGroups');
            if (conditionGroups.children.length > 1) {
                conditionGroup.remove();
            } else {
                alert('至少需要保留一个条件');
            }
        }

        // 保存新规则
        function saveNewRule() {
            const form = document.getElementById('newRuleForm');

            // 验证必填字段
            const ruleName = document.getElementById('ruleName').value;
            const riskLevel = document.getElementById('riskLevel').value;
            const responseAction = document.getElementById('responseAction').value;

            if (!ruleName || !riskLevel || !responseAction) {
                alert('请填写所有必填字段');
                return;
            }

            // 收集条件数据
            const conditions = {
                deviceRisks: [],
                behaviorRisks: [],
                environmentRisks: []
            };

            // 收集设备风险条件
            document.querySelectorAll('.device-risk-toggle.active').forEach(toggle => {
                const card = toggle.closest('.device-risk-card');
                const riskType = toggle.getAttribute('onclick').match(/'(\w+)'/)[1];
                const title = card.querySelector('.device-risk-title').textContent;
                const checkedOptions = [];

                card.querySelectorAll('.device-risk-checkbox:checked').forEach(checkbox => {
                    checkedOptions.push({
                        id: checkbox.id,
                        label: checkbox.nextElementSibling.textContent
                    });
                });

                if (checkedOptions.length > 0) {
                    conditions.deviceRisks.push({
                        type: riskType,
                        title: title,
                        options: checkedOptions
                    });
                }
            });

            // 收集行为风险条件
            document.querySelectorAll('#behaviorConditionGroups .condition-group').forEach((group, groupIndex) => {
                const logic = group.querySelector('.logic-select').value;
                const groupConditions = [];

                group.querySelectorAll('.condition-row').forEach(row => {
                    const field = row.querySelector('select:nth-child(1)').value;
                    const operator = row.querySelector('select:nth-child(2)').value;
                    const value = row.querySelector('input').value;

                    if (field && operator && value) {
                        groupConditions.push({ field, operator, value });
                    }
                });

                if (groupConditions.length > 0) {
                    conditions.behaviorRisks.push({
                        groupIndex: groupIndex + 1,
                        logic: logic,
                        conditions: groupConditions
                    });
                }
            });

            // 收集环境风险条件
            document.querySelectorAll('#environmentConditionGroups .condition-group').forEach((group, groupIndex) => {
                const logic = group.querySelector('.logic-select').value;
                const groupConditions = [];

                group.querySelectorAll('.condition-row').forEach(row => {
                    const field = row.querySelector('select:nth-child(1)').value;
                    const operator = row.querySelector('select:nth-child(2)').value;
                    const value = row.querySelector('input').value;

                    if (field && operator && value) {
                        groupConditions.push({ field, operator, value });
                    }
                });

                if (groupConditions.length > 0) {
                    conditions.environmentRisks.push({
                        groupIndex: groupIndex + 1,
                        logic: logic,
                        conditions: groupConditions
                    });
                }
            });

            // 验证至少有一个条件
            const totalConditions = conditions.deviceRisks.length + conditions.behaviorRisks.length + conditions.environmentRisks.length;
            if (totalConditions === 0) {
                alert('请至少配置一个触发条件');
                return;
            }

            // 构建新规则对象
            const newRule = {
                name: ruleName,
                riskLevel: riskLevel,
                description: document.getElementById('ruleDescription').value,
                conditions: conditions,
                responseAction: responseAction,
                status: document.getElementById('ruleStatus').value,
                priority: document.getElementById('rulePriority').value,
                createTime: new Date().toISOString()
            };

            console.log('新建规则:', newRule);
            alert('规则创建成功！\n\n规则详情：\n' +
                  '设备风险项: ' + conditions.deviceRisks.length + ' 个\n' +
                  '行为风险项: ' + conditions.behaviorRisks.length + ' 个\n' +
                  '环境风险项: ' + conditions.environmentRisks.length + ' 个');
            closeNewRuleModal();

            // 这里可以添加实际的API调用来保存规则
            // 然后刷新规则列表
        }

        // 分页功能
        let currentPage = 1;
        let currentRulePage = 1;

        // 风险处置记录分页
        function changePage(direction) {
            const totalPages = parseInt(document.getElementById('totalPages').textContent);
            if (direction === 'prev' && currentPage > 1) {
                currentPage--;
            } else if (direction === 'next' && currentPage < totalPages) {
                currentPage++;
            }
            updatePageDisplay();
            loadRecordsPage(currentPage);
        }

        function goToPage(page) {
            currentPage = page;
            updatePageDisplay();
            loadRecordsPage(page);
        }

        function updatePageDisplay() {
            document.getElementById('currentPage').textContent = currentPage;
            document.querySelectorAll('.pagination-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelector(`[onclick="goToPage(${currentPage})"]`)?.classList.add('active');

            const totalPages = parseInt(document.getElementById('totalPages').textContent);
            document.getElementById('prevBtn').disabled = currentPage === 1;
            document.getElementById('nextBtn').disabled = currentPage === totalPages;
        }

        function loadRecordsPage(page) {
            // 这里可以调用API加载对应页面的数据
            console.log(`加载第${page}页的风险处置记录`);
        }

        // 规则管理分页
        function changeRulePage(direction) {
            const totalPages = parseInt(document.getElementById('totalRulePages').textContent);
            if (direction === 'prev' && currentRulePage > 1) {
                currentRulePage--;
            } else if (direction === 'next' && currentRulePage < totalPages) {
                currentRulePage++;
            }
            updateRulePageDisplay();
            loadRulesPage(currentRulePage);
        }

        function goToRulePage(page) {
            currentRulePage = page;
            updateRulePageDisplay();
            loadRulesPage(page);
        }

        function updateRulePageDisplay() {
            document.getElementById('currentRulePage').textContent = currentRulePage;
            const totalPages = parseInt(document.getElementById('totalRulePages').textContent);
            document.getElementById('rulePrevBtn').disabled = currentRulePage === 1;
            document.getElementById('ruleNextBtn').disabled = currentRulePage === totalPages;
        }

        function loadRulesPage(page) {
            // 这里可以调用API加载对应页面的规则数据
            console.log(`加载第${page}页的风险规则`);
        }



        // 跳转到案例审计模块
        function goToCaseAudit(caseId) {
            // 跳转到案例审计页面，并传递案例ID
            window.location.href = `case-audit.html?caseId=${caseId}`;
        }

        // 筛选功能
        function applyFilters() {
            const riskType = document.getElementById('riskTypeFilter').value;
            const triggerRule = document.getElementById('triggerRuleFilter').value;
            const responseAction = document.getElementById('responseActionFilter').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            console.log('应用筛选条件:', {
                riskType, triggerRule, responseAction, startDate, endDate
            });

            // 这里可以调用API进行筛选
            alert('筛选功能已应用！\n' +
                  `风险类型: ${riskType || '全部'}\n` +
                  `触发规则: ${triggerRule || '全部'}\n` +
                  `响应动作: ${responseAction || '全部'}\n` +
                  `时间范围: ${startDate || '不限'} 至 ${endDate || '不限'}`);
        }

        function resetFilters() {
            document.getElementById('riskTypeFilter').value = '';
            document.getElementById('triggerRuleFilter').value = '';
            document.getElementById('responseActionFilter').value = '';
            document.getElementById('startDate').value = '';
            document.getElementById('endDate').value = '';
            console.log('筛选条件已重置');
        }

        // 规则详情功能
        function editRule(ruleId) {
            alert(`编辑规则: ${ruleId}（功能开发中）`);
        }

        function viewRuleDetail(ruleId) {
            // 模拟规则详情数据
            const ruleDetails = {
                'rule_001': {
                    name: '模拟器检测拦截',
                    riskLevel: '高风险',
                    description: '检测并拦截模拟器设备的访问请求，防范自动化攻击',
                    conditions: {
                        deviceRisks: [
                            { type: '模拟器检测', options: ['检测到模拟器时触发'] }
                        ]
                    },
                    responseAction: '直接拒绝',
                    status: '已启用',
                    priority: '1',
                    createTime: '2024-01-15 10:30:00',
                    updateTime: '2024-01-20 14:25:00',
                    creator: '张三',
                    triggerCount: 1247,
                    successRate: '98.5%'
                },
                'rule_002': {
                    name: '高频请求限制',
                    riskLevel: '中风险',
                    description: '限制短时间内的高频请求，防范暴力破解和批量操作',
                    conditions: {
                        behaviorRisks: [
                            { field: '请求次数', operator: '大于', value: '5', timeWindow: '1分钟' }
                        ]
                    },
                    responseAction: '提升验证难度',
                    status: '已启用',
                    priority: '2',
                    createTime: '2024-01-10 09:15:00',
                    updateTime: '2024-01-18 16:40:00',
                    creator: '李四',
                    triggerCount: 856,
                    successRate: '95.2%'
                }
            };

            const rule = ruleDetails[ruleId] || ruleDetails['rule_001'];

            const detailHtml = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4 style="margin-bottom: 16px; color: var(--text-color);">基本信息</h4>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                            <div><strong>规则名称:</strong> ${rule.name}</div>
                            <div><strong>风险等级:</strong> <span class="risk-badge risk-${rule.riskLevel === '高风险' ? 'high' : rule.riskLevel === '中风险' ? 'medium' : 'low'}">${rule.riskLevel}</span></div>
                            <div><strong>响应动作:</strong> ${rule.responseAction}</div>
                            <div><strong>规则状态:</strong> <span style="color: var(--success-color);">${rule.status}</span></div>
                            <div><strong>优先级:</strong> ${rule.priority}</div>
                            <div><strong>创建人:</strong> ${rule.creator}</div>
                        </div>
                    </div>
                    <div>
                        <h4 style="margin-bottom: 16px; color: var(--text-color);">统计信息</h4>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                            <div><strong>触发次数:</strong> ${rule.triggerCount}</div>
                            <div><strong>成功率:</strong> ${rule.successRate}</div>
                            <div><strong>创建时间:</strong> ${rule.createTime}</div>
                            <div><strong>更新时间:</strong> ${rule.updateTime}</div>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 20px;">
                    <h4 style="margin-bottom: 12px; color: var(--text-color);">规则描述</h4>
                    <p style="color: var(--text-color-secondary); line-height: 1.5;">${rule.description}</p>
                </div>
                <div style="margin-top: 20px;">
                    <h4 style="margin-bottom: 12px; color: var(--text-color);">触发条件</h4>
                    <div style="background-color: var(--bg-color); padding: 16px; border-radius: 8px;">
                        ${rule.conditions.deviceRisks ? rule.conditions.deviceRisks.map(risk =>
                            `<div style="margin-bottom: 8px;">• 设备风险: ${risk.type} - ${risk.options.join(', ')}</div>`
                        ).join('') : ''}
                        ${rule.conditions.behaviorRisks ? rule.conditions.behaviorRisks.map(risk =>
                            `<div style="margin-bottom: 8px;">• 行为风险: ${risk.field} ${risk.operator} ${risk.value} (${risk.timeWindow})</div>`
                        ).join('') : ''}
                    </div>
                </div>
            `;

            document.getElementById('ruleDetailContent').innerHTML = detailHtml;
            document.getElementById('ruleDetailModal').classList.add('active');
        }

        function closeRuleDetailModal() {
            document.getElementById('ruleDetailModal').classList.remove('active');
        }

        // 处置记录详情功能
        function viewRecordDetail(recordId) {
            // 模拟处置记录详情数据
            const recordDetails = {
                'record_001': {
                    time: '2024-01-25 11:23:45',
                    riskType: '模拟器检测',
                    userDevice: 'user_11****34',
                    riskLevel: '高风险',
                    triggerRule: '模拟器检测拦截',
                    responseAction: '直接拒绝',
                    deviceInfo: {
                        deviceId: 'ff4e8a2b...c1a0d5f8',
                        deviceType: 'Android模拟器',
                        osVersion: 'Android 9.0',
                        appVersion: '2.1.5',
                        ip: '*************',
                        location: '北京市朝阳区'
                    },
                    riskDetails: {
                        detectedFeatures: ['模拟器特征码', '虚拟设备标识', '调试环境'],
                        confidenceScore: '95.8%',
                        riskScore: 85
                    },
                    actionResult: {
                        executed: true,
                        executionTime: '2024-01-25 11:23:46',
                        result: '访问已拒绝',
                        responseTime: '120ms'
                    }
                }
            };

            const record = recordDetails[recordId] || recordDetails['record_001'];

            const detailHtml = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4 style="margin-bottom: 16px; color: var(--text-color);">基本信息</h4>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                            <div><strong>发生时间:</strong> ${record.time}</div>
                            <div><strong>风险类型:</strong> ${record.riskType}</div>
                            <div><strong>用户/设备:</strong> ${record.userDevice}</div>
                            <div><strong>风险等级:</strong> <span class="risk-badge risk-high">${record.riskLevel}</span></div>
                            <div><strong>触发规则:</strong> ${record.triggerRule}</div>
                            <div><strong>响应动作:</strong> ${record.responseAction}</div>
                        </div>
                    </div>
                    <div>
                        <h4 style="margin-bottom: 16px; color: var(--text-color);">设备信息</h4>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                            <div><strong>设备ID:</strong> ${record.deviceInfo.deviceId}</div>
                            <div><strong>设备类型:</strong> ${record.deviceInfo.deviceType}</div>
                            <div><strong>系统版本:</strong> ${record.deviceInfo.osVersion}</div>
                            <div><strong>应用版本:</strong> ${record.deviceInfo.appVersion}</div>
                            <div><strong>IP地址:</strong> ${record.deviceInfo.ip}</div>
                            <div><strong>地理位置:</strong> ${record.deviceInfo.location}</div>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 20px;">
                    <h4 style="margin-bottom: 12px; color: var(--text-color);">风险详情</h4>
                    <div style="background-color: var(--bg-color); padding: 16px; border-radius: 8px;">
                        <div style="margin-bottom: 12px;"><strong>检测特征:</strong> ${record.riskDetails.detectedFeatures.join(', ')}</div>
                        <div style="margin-bottom: 12px;"><strong>置信度:</strong> ${record.riskDetails.confidenceScore}</div>
                        <div><strong>风险评分:</strong> ${record.riskDetails.riskScore}/100</div>
                    </div>
                </div>
                <div style="margin-top: 20px;">
                    <h4 style="margin-bottom: 12px; color: var(--text-color);">处置结果</h4>
                    <div style="background-color: var(--bg-color); padding: 16px; border-radius: 8px;">
                        <div style="margin-bottom: 12px;"><strong>执行状态:</strong> <span style="color: var(--success-color);">${record.actionResult.executed ? '已执行' : '未执行'}</span></div>
                        <div style="margin-bottom: 12px;"><strong>执行时间:</strong> ${record.actionResult.executionTime}</div>
                        <div style="margin-bottom: 12px;"><strong>处置结果:</strong> ${record.actionResult.result}</div>
                        <div><strong>响应时间:</strong> ${record.actionResult.responseTime}</div>
                    </div>
                </div>
            `;

            document.getElementById('recordDetailContent').innerHTML = detailHtml;
            document.getElementById('recordDetailModal').classList.add('active');
        }

        function closeRecordDetailModal() {
            document.getElementById('recordDetailModal').classList.remove('active');
        }

        function goToCaseAuditFromDetail() {
            closeRecordDetailModal();
            goToCaseAudit('case_from_detail');
        }

        // 点击模态框外部关闭
        document.getElementById('newRuleModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeNewRuleModal();
            }
        });

        document.getElementById('ruleDetailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRuleDetailModal();
            }
        });

        document.getElementById('recordDetailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRecordDetailModal();
            }
        });

        // 子菜单展开/收起功能
        function toggleSubmenu(element) {
            element.classList.toggle('expanded');
        }
    </script>
</body>
</html>
