<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSID Platform - 系统管理</title>
    <style>
        :root { --bg-color: #f6f8fb; --sidebar-bg-color: #ffffff; --card-bg-color: #ffffff; --text-color: #1e293b; --text-color-secondary: #64748b; --border-color: #e5eaf3; --primary-color: #2d78f4; --primary-color-light: #f0f6ff; --shadow-color: rgba(0,0,0,0.06); --icon-color: #64748b; --icon-hover-bg: #f1f5f9; }
        [data-theme="dark"] { --bg-color: #111827; --sidebar-bg-color: #1f2937; --card-bg-color: #1f2937; --text-color: #f9fafb; --text-color-secondary: #9ca3af; --border-color: #374151; --primary-color: #3b82f6; --primary-color-light: #252e3d; --shadow-color: rgba(0,0,0,0.2); --icon-color: #9ca3af; --icon-hover-bg: #374151; }
        * { margin: 0; padding: 0; box-sizing: border-box; font-family: "PingFang SC", "Microsoft YaHei", sans-serif; }
        html, body { height: 100%; overflow: hidden; }
        body { background-color: var(--bg-color); color: var(--text-color); transition: background-color 0.3s, color 0.3s; display: flex; }
        .sidebar { width: 260px; background-color: var(--sidebar-bg-color); border-right: 1px solid var(--border-color); display: flex; flex-direction: column; height: 100%; flex-shrink: 0; transition: background-color 0.3s, border-color 0.3s; }
        .main-wrapper { flex-grow: 1; height: 100%; overflow-y: auto; }
        .main-content { padding: 24px; }
        .sidebar-header { display: flex; align-items: center; padding: 0 24px; height: 64px; border-bottom: 1px solid var(--border-color); flex-shrink: 0; }
        .sidebar-logo { font-size: 22px; font-weight: 700; color: var(--primary-color); }
        .sidebar-nav { flex-grow: 1; padding: 16px 0; }
        .nav-list { list-style: none; }
        .nav-item a { display: flex; align-items: center; gap: 12px; padding: 12px 24px; margin: 4px 16px; border-radius: 8px; color: var(--text-color-secondary); text-decoration: none; font-weight: 500; transition: background-color 0.2s, color 0.2s; }
        .nav-item a:hover { background-color: var(--primary-color-light); color: var(--primary-color); }
        .nav-item.active a { background-color: var(--primary-color-light); color: var(--primary-color); font-weight: 600; }
        .nav-icon { width: 20px; height: 20px; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .page-title { font-size: 24px; font-weight: 700; }
        .header-actions { display: flex; align-items: center; gap: 16px; }
        .theme-toggle { background-color: var(--icon-hover-bg); border: none; border-radius: 50%; width: 36px; height: 36px; cursor: pointer; display: flex; align-items: center; justify-content: center; color: var(--icon-color); transition: background-color 0.2s; }
        .theme-toggle .icon { width: 20px; height: 20px; }
        #moon-icon { display: none; }
        .card { background-color: var(--card-bg-color); border-radius: 12px; margin-bottom: 24px; box-shadow: 0 4px 12px var(--shadow-color); border: 1px solid var(--border-color); overflow: hidden; transition: background-color 0.3s, border-color 0.3s; }
        .card-header { display: flex; justify-content: space-between; align-items: center; padding: 16px 24px; border-bottom: 1px solid var(--border-color); }
        .card-title { font-size: 17px; font-weight: 600; color: var(--text-color); }
        .card-content { padding: 24px; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { text-align: left; padding: 12px 16px; border-bottom: 1px solid var(--border-color); font-size: 14px; }
        .table th { color: var(--text-color-secondary); font-weight: 500; }
        .btn { padding: 8px 16px; border-radius: 6px; border: none; font-size: 14px; cursor: pointer; font-weight: 500; transition: all 0.2s; }
        .btn-primary { background-color: var(--primary-color); color: white; }
        .btn-secondary { background-color: var(--icon-hover-bg); color: var(--text-color); border: 1px solid var(--border-color); }
        .btn-danger { background-color: rgba(255, 59, 48, 0.15); color: #ff3b30; }
        .action-buttons { display: flex; gap: 8px; }
        .tabs { display: flex; border-bottom: 1px solid var(--border-color); }
        .tab-button { padding: 12px 20px; cursor: pointer; background: none; border: none; font-size: 15px; color: var(--text-color-secondary); font-weight: 500; border-bottom: 2px solid transparent; }
        .tab-button.active { color: var(--primary-color); border-bottom-color: var(--primary-color); }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
    </style>
</head>
<body data-theme="light">

    <aside class="sidebar">
        <div class="sidebar-header"><h1 class="sidebar-logo">SSID Platform</h1></div>
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item"><a href="dashboard.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path></svg><span>首页</span></a></li>
                <li class="nav-item"><a href="configuration.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg><span>配置管理</span></a></li>
                <li class="nav-item"><a href="analytics.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg><span>数据统计</span></a></li>
                <li class="nav-item"><a href="risk-management.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg><span>风险管控</span></a></li>
                <li class="nav-item"><a href="case-audit.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg><span>案例审计</span></a></li>
                <li class="nav-item"><a href="feature-sets.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path></svg><span>特征库管理</span></a></li>
                <li class="nav-item"><a href="distribution.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path></svg><span>资源分发</span></a></li>
                <li class="nav-item active"><a href="settings.html"><svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 16v-2m8-8h2M4 12H2m15.364 6.364l-1.414-1.414M6.05 6.05L4.636 4.636m12.728 12.728L15.95 15.95M6.05 17.95l1.414-1.414M12 18a6 6 0 100-12 6 6 0 000 12z"></path></svg><span>系统管理</span></a></li>
            </ul>
        </nav>
    </aside>

    <div class="main-wrapper">
        <main class="main-content">
            <header class="page-header">
                <h1 class="page-title">系统管理</h1>
                <div class="header-actions">
                    <button class="theme-toggle" id="theme-toggle-btn" title="切换亮/暗模式"><svg id="sun-icon" class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path></svg><svg id="moon-icon" class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path></svg></button>
                </div>
            </header>

            <div class="card">
                <div class="tabs">
                    <button class="tab-button active" onclick="openTab(event, 'users')">用户管理</button>
                    <button class="tab-button" onclick="openTab(event, 'roles')">角色与权限</button>
                    <button class="tab-button" onclick="openTab(event, 'logs')">操作日志</button>
                </div>
                
                <div id="users" class="tab-content active">
                    <div class="card-header"><h2 class="card-title">用户列表</h2><button class="btn btn-primary">+ 添加用户</button></div>
                    <div style="padding: 0;"><table class="table">
                        <thead><tr><th>用户名</th><th>角色</th><th>创建时间</th><th>上次登录</th><th>操作</th></tr></thead>
                        <tbody>
                            <tr><td>admin</td><td>超级管理员</td><td>2023-01-01</td><td>2023-08-10 10:30</td><td class="action-buttons"><button class="btn btn-secondary">编辑</button><button class="btn btn-danger">删除</button></td></tr>
                            <tr><td>operator01</td><td>运营分析师</td><td>2023-02-15</td><td>2023-08-09 15:45</td><td class="action-buttons"><button class="btn btn-secondary">编辑</button><button class="btn btn-danger">删除</button></td></tr>
                            <tr><td>risk-analyst</td><td>风控师</td><td>2023-03-10</td><td>2023-08-10 09:12</td><td class="action-buttons"><button class="btn btn-secondary">编辑</button><button class="btn btn-danger">删除</button></td></tr>
                        </tbody>
                    </table></div>
                </div>

                <div id="roles" class="tab-content">
                    <div class="card-header"><h2 class="card-title">角色列表</h2><button class="btn btn-primary">+ 添加角色</button></div>
                    <div style="padding: 0;"><table class="table">
                         <thead><tr><th>角色名称</th><th>权限描述</th><th>用户数</th><th>操作</th></tr></thead>
                        <tbody>
                            <tr><td>超级管理员</td><td>拥有所有模块的完全访问权限</td><td>1</td><td class="action-buttons"><button class="btn btn-secondary">编辑权限</button></td></tr>
                            <tr><td>运营分析师</td><td>可访问仪表盘、数据统计、案例审计</td><td>5</td><td class="action-buttons"><button class="btn btn-secondary">编辑权限</button><button class="btn btn-danger">删除</button></td></tr>
                            <tr><td>风控师</td><td>可访问风险管控、案例审计</td><td>3</td><td class="action-buttons"><button class="btn btn-secondary">编辑权限</button><button class="btn btn-danger">删除</button></td></tr>
                        </tbody>
                    </table></div>
                </div>

                <div id="logs" class="tab-content">
                    <div class="card-header"><h2 class="card-title">操作日志</h2><button class="btn btn-secondary">导出</button></div>
                     <div style="padding: 0;"><table class="table">
                        <thead><tr><th>时间</th><th>操作人</th><th>IP地址</th><th>操作详情</th></tr></thead>
                        <tbody>
                            <tr><td>2023-08-10 11:05:12</td><td>admin</td><td>************</td><td>创建了风险规则 "新规则A"</td></tr>
                            <tr><td>2023-08-10 10:59:34</td><td>operator01</td><td>*********</td><td>查询了案例 "req_abc...xyz"</td></tr>
                            <tr><td>2023-08-09 16:20:05</td><td>admin</td><td>************</td><td>停用了风险规则 "旧规则B"</td></tr>
                        </tbody>
                    </table></div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // --- THEME SCRIPT ---
        const themeToggleBtn = document.getElementById('theme-toggle-btn');
        const sunIcon = document.getElementById('sun-icon');
        const moonIcon = document.getElementById('moon-icon');
        const body = document.body;
        const applyTheme = (theme) => {
            body.setAttribute('data-theme', theme);
            if (theme === 'dark') { sunIcon.style.display = 'none'; moonIcon.style.display = 'block'; } else { sunIcon.style.display = 'block'; moonIcon.style.display = 'none'; }
        };
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        const defaultTheme = savedTheme || (prefersDark ? 'dark' : 'light');
        applyTheme(defaultTheme);
        themeToggleBtn.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            localStorage.setItem('theme', newTheme);
            applyTheme(newTheme);
        });

        // --- TABS SCRIPT ---
        function openTab(evt, tabName) {
            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("tab-content");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            tablinks = document.getElementsByClassName("tab-button");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }
    </script>
</body>
</html>```
</details>
