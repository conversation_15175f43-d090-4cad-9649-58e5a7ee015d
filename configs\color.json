// 
// MotionType 0:ready、1:blink、2:mouth、3:nod、4:yaw、5:color、6:silent
// ColorType: 0:UNKNOWN, 1:<PERSON><PERSON>C<PERSON>, 2:RED, 3:<PERSON><PERSON><PERSON>, 4:BLUE, 5:YEL<PERSON><PERSON>, [6:<PERSON><PERSON><PERSON>, 7:<PERSON><PERSON><PERSON>, 8:WHITE]
//
{
  "config": {
    "enable_multi_faces": true,
    "enable_data_collection": false,
    "retry_times": 3,
    "track_config": {
      "detection_period": 5,
      "detection_threshold": 0.6,
      "predict_threshold": 0.3,
      "iou_threshold": 0.5,
      "select_period": 5
    },
    "quality_config": {
      "enable_headpose": true,
      "enable_occlusion": true,
      "enable_eyestate": true,
      "enable_mouthstate": true,
      "enable_illuminate": true,
      "enable_blur": true,
      "enable_position": true,
      "headpose": {
        "pitch_upper_threshold": 30.0,
        "pitch_lower_threshold": -30.0,
        "roll_upper_threshold": 30.0,
        "roll_lower_threshold": -30.0,
        "yaw_upper_threshold": 30.0,
        "yaw_lower_threshold": -30.0
      },
      "occlusion": {
        "enable_mouth": true,
        "enable_nose": true,
        "enable_left_cheek": true,
        "enable_right_cheek": true,
        "enable_left_brow": false,
        "enable_right_brow": false,
        "enable_left_eye": true,
        "enable_right_eye": true,
        "mouth_occlusion_threshold": 0.7,
        "nose_occlusion_threshold": 0.8,
        "left_cheek_occlusion_threshold": 0.3,
        "right_cheek_occlusion_threshold": 0.3,
        "left_brow_occlusion_threshold": 0.6,
        "right_brow_occlusion_threshold": 0.6,
        "left_eye_occlusion_threshold": 0.5,
        "right_eye_occlusion_threshold": 0.5
      },
      "eyestate": {
        "eye_valid_threshold": 0.8,
        "left_eye_open_threshold": 0.7,
        "right_eye_open_threshold": 0.7
      },
      "mouthstate": {
        "mouth_open_threshold": 0.7
      },
      "illuminate": {
        "over_dark_threshold": 0.2,
        "over_glare_threshold": 0.8
      },
      "blur": {
        "blur_threshold": 0.44
      },
      "position": {
        "iou_threshold": 0.95,
        "move_left_threshold": 0.13,
        "move_right_threshold": 0.13,
        "move_up_threshold": 0.33,
        "move_down_threshold": 0.13,
        "move_forward_threshold": 0.25,
        "move_backward_threshold": 0.85,
        "edge_threshold": 0.05
      }
    },
    "ready_config": {
      "check_eye_closed": true,
      "eye_valid_threshold": 0.9,
      "eye_close_threshold": 0.3,
      "mouth_open_threshold": 0.7,
      "upper_headpose_angle": 20.0,
      "lower_headpose_angle": -20.0,
      "repeat_num": 2,
      "select_interval": 150,
      "select_num": 3,
      "timeout": 10000
    },
    "blink_config": {
      "eye_valid_threshold": 0.9,
      "eye_close_threshold": 0.3,
      "eye_open_threshold": 0.9,
      "repeat_num": 2,
      "select_interval": 150,
      "select_num": 3,
      "timeout": 10000
    },
    "mouth_config": {
      "mouth_open_threshold": 0.55,
      "mouth_close_threshold": 0.80,
      "repeat_num": 2,
      "select_interval": 150,
      "select_num": 3,
      "timeout": 10000
    },
    "nod_config": {
      "nod_dist": 10.0,
      "repeat_num": 2,
      "select_interval": 150,
      "select_num": 3,
      "timeout": 10000
    },
    "yaw_config": {
      "yaw_dist": 15.0,
      "repeat_num": 2,
      "select_interval": 150,
      "select_num": 3,
      "timeout": 10000
    },
    "color_config": {
      "color_sequence": [
      ],
      // 参考：ColorType
      "color_duration": 500.0,
      "diff_num": 1,
      "iou_threshold": 0.8,
      "skip_first_frames": 100,
      "max_select_num": 3,
      "timeout": 10000
    }
  },
  "motions": [
  ],
  // 参考：MotionType
  "color_map": [
  ]
}