sdk:
  timeout: 180000
  ttl: 10 #minutes
  log-path: /images/sdk-log/
  interactive-motion:
    type: 1                          #动作类型  0固定，1随机（从random-sequence中随机random-number个动作）
    fix-sequence: 1,2,3,4            #固定动作序列 1:blink、2:mouth、3:nod、4:yaw
    random-sequence: 1,2,3,4         #1动作序列 1:blink、2:mouth、3:nod、4:yaw
    random-number: 4                 #随机个数，值要小于等于sequence
  color-motion:
    type: 2   #【0,1,2,3】  0表示无动作序列（只做预检和炫彩），1表示固定动作序列（fix-sequence），2表示随机一个动作序列（random-one-sequence），3表示随机两个动作序列（random-two-sequence-group1、random-two-sequence-group2各随机取一个动作）
    fix-sequence: 1,4    #固定动作序列，至少配置一个动作序列
    random-one-sequence: 1,2 #随机一个动作序列，至少配置一个动作序列
    random-two-sequence-group1: 1,2 #随机两个动作序列，group1、group2至少配置一个动作序列
    random-two-sequence-group2: 3,4 #随机两个动作序列，group1、group2至少配置一个动作序列
  color:
    num: 6 #炫彩生成几种颜色序列  最小值为3
    live-interval: 3 #炫彩颜色帧活体间隔。N表示每N帧做一次活体检测，默认值为3。1表示每帧都做活体检测。

judge-rule:
  silent:
    verify-threshold: 0.35 # 比对阈值
    consistency-score: 0.95 # 图片一致性阈值
    consistency-pass-percent: 0.5 # 通过比例
    best-img-live-threshold: 0.88 # 活体大模型阈值
    live-pass-percent: 0.5
    live-threshold: 0.85
    occlusion-percent: 0.5
  interactive:
    verify-threshold: 0.35 # 比对阈值
    best-img-live-threshold: 0.88 # 活体大模型阈值
    live-pass-percent: 0.5
    live-threshold: 0.85
    occlusion-percent: 0.5
  color:
    verify-threshold: 0.35 # 比对阈值
    best-img-live-threshold: 0.88 # 活体大模型阈值
    live-pass-percent: 0.5
    live-threshold: 0.85
    diff-threshold: 0.5 # diff比较分数
    occlusion-percent: 0.5
    # 解光相关
    light-level: 1 #解光等级  0 低 1 中 2 高
    # color比较分数 light-level 0 -> 0.9，light-level 1 -> 0.9，light-level 2 -> 0.9
    color-threshold: 0.9
